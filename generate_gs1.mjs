#!/usr/bin/env node

function showUsage() {
  console.info('Usage: node generate_gs1.js <count> <13_digit_barcode>')
  console.info('Example: node generate_gs1.js 10 1234567890123')
  process.exit(1)
}

function validateBarcode(barcode) {
  if (barcode.length !== 13) {
    console.error('Error: Barcode must be exactly 13 characters long')
    process.exit(1)
  }

  if (!/^\d{13}$/.test(barcode)) {
    console.error('Error: Barcode must contain only digits')
    process.exit(1)
  }
}

function generateRandomSuffix(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length)
    result += chars[randomIndex]
  }

  return result
}

function generateGS1Barcodes(count, barcode) {
  const barcodePrefix = `010${barcode}21`
  const prefixLength = barcodePrefix.length
  const totalLength = 38
  const suffixLength = totalLength - prefixLength

  const barcodes = []

  for (let i = 1; i <= count; i++) {
    const suffix = generateRandomSuffix(suffixLength)
    const fullBarcode = `${barcodePrefix}${suffix}`
    barcodes.push(fullBarcode)
    console.info(fullBarcode)
  }

  return barcodes
}

function main() {
  const args = process.argv.slice(2)

  if (args.length !== 2) {
    showUsage()
  }

  const count = parseInt(args[0], 10)
  const barcode = args[1]

  if (isNaN(count) || count <= 0) {
    console.error('Error: Count must be a positive number')
    process.exit(1)
  }

  validateBarcode(barcode)
  generateGS1Barcodes(count, barcode)
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { generateGS1Barcodes, generateRandomSuffix, validateBarcode }
