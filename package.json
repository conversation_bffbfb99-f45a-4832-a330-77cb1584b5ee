{"name": "cloudretail", "version": "1.0.0", "private": true, "type": "module", "workspaces": ["apps/api", "apps/web-ui"], "scripts": {"api:compose:down": "npm -w apps/api run compose:down", "api:compose:up": "npm -w apps/api run compose:up", "api:lint": "npm -w apps/api run lint", "api:start": "npm -w apps/api run start", "api:test": "npm -w apps/api run test", "api:typecheck": "npm -w apps/api run typecheck", "format": "npm-run-all format:schemas format:sources", "format:schemas": "npx -w apps/api prisma format", "format:sources": "prettier --ignore-unknown --list-different --write .", "lint": "npm-run-all api:lint web-ui:lint", "optimize-imports": "node optimize-imports.mjs", "prepare": "husky", "start": "npm-run-all -p api:start web-ui:start", "test": "npm-run-all api:test web-ui:test", "typecheck": "npm-run-all api:typecheck web-ui:typecheck", "web-ui:lint": "npm -w apps/web-ui run lint", "web-ui:start": "npm -w apps/web-ui run start", "web-ui:test": "npm -w apps/web-ui run test", "web-ui:typecheck": "npm -w apps/web-ui run typecheck"}, "devDependencies": {"husky": "~9.1.7", "lint-staged": "~16.1.6", "npm-run-all": "~4.1.5", "prettier": "~3.6.2", "prettier-plugin-sql": "~0.19.2", "prettier-plugin-tailwindcss": "~0.6.14", "ts-morph": "~27.0.0"}}