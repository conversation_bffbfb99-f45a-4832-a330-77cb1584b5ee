# Instructions/Rules for CloudRetail

## Project Architecture

This is a **TypeScript monorepo** with two main apps: `apps/api` (Node.js backend) and `apps/web-ui` (React frontend). The project uses npm workspaces with workspace-level scripts.

### Key Technologies

- **API**: tRPC + Prisma + PostgreSQL + find-my-way router
- **Frontend**: React 19 + React Router v7 + TanStack Query + Redux Toolkit
- **Database**: PostgreSQL with Prisma ORM
- **Styling**: Tailwind CSS v4 with @tw-classed/react
- **Deployment**: Docker with compose setup

## Development Workflows

### Essential Commands

```bash
# Start both API and web-ui in development
npm start

# Database operations (run from root)
npm run format:schemas        # Format Prisma schema
npx -w apps/api prisma migrate dev --name <description>

# API-specific operations
npm run api:compose:up        # Start PostgreSQL + DbGate
npm run api:compose:down      # Stop Docker services
npm run api:start            # Start API only

# Code quality
npm run optimize-imports     # Organize imports across workspaces
npm run format              # Format all code with Prettier
```

### Docker Development

- PostgreSQL runs on `localhost:5432` (postgres/postgres)
- DbGate database UI on `localhost:3000`
- Use `apps/api/docker-compose.yml` for local development

## Database & Business Logic

### Core Domain Models

The system models a **retail/inventory management** system with these key entities:

- **Companies** (multi-tenant) → **Users** (with permissions)
- **Products** → **ProductUnits** (with barcode/multiplier hierarchy)
- **Warehouses** → **ProductInventories** → **ProductTransactions**
- **BusinessParties** (suppliers/customers) → **Invoices** → **Payments**
- **DataMatrix** requests/responses for product serialization

### Database Patterns

- All tables are company-scoped (multi-tenant by `company_id`)
- Money stored as **integers** (cents), not decimals
- Dates stored as `@db.Date` (no time component)
- Heavy use of **composite indexes** for performance
- Transaction tables link via nullable foreign keys (`purchase_invoice_item_id`, etc.)

## API Patterns

### tRPC Router Structure

```typescript
// Each domain has its own router in apps/api/src/routers/
export const businessPartiesRouter = router({
  findMany, // List with filters
  findOne, // Get by ID
  createOne, // Create new
  updateOne, // Update existing
  deleteOne, // Delete by ID
})
```

### Authentication Flow

- Bearer token authentication via `parseAuth()` utility
- Token stored in `localStorage` on frontend (`TOKEN_KEY` constant)
- `protectedProcedure` middleware for authenticated endpoints
- Manual auth middleware (not using external auth providers)

### Custom HTTP Routes

Beyond tRPC, there's a **custom route** for file uploads:

```typescript
// apps/api/src/router.ts
router.post('/datamatrix-responses/:id', async (request, response, params) => {
  // CSV file processing with formidable + readline
})
```

## Frontend Patterns

### Component Architecture

- Pages in `src/pages/` with **route-based code splitting**
- Each page domain has its own `route.ts` file (React Router v7 pattern)
- Shared components in `src/components/` with `index.ts` barrel exports
- Redux for global state, tRPC for server state

### tRPC Integration

```typescript
// Type-safe API calls
const { data, isLoading } = trpc.businessParties.findMany.useQuery({
  company_id: companyId,
})

// Mutations with automatic invalidation
const createMutation = trpc.businessParties.createOne.useMutation({
  onSuccess: () => utils.businessParties.findMany.invalidate(),
})
```

### Styling Conventions

- Use `@tw-classed/react` for component styling with variants
- Tailwind v4 with automatic class detection
- `tailwind-merge` for conditional class merging

## Project-Specific Conventions

### File Organization

- **Barrel exports**: Most directories have `index.ts` that re-exports everything
- **Router procedures**: Each tRPC endpoint is a separate file (`findMany.ts`, `createOne.ts`)
- **Utility functions**: Organized in `apps/api/src/utils/` with descriptive names

### Import Organization

- Run `npm run optimize-imports` to organize imports with ts-morph
- Uses `.js` extensions in TypeScript imports (ESM requirement)
- Relative imports for local files, absolute for external packages

### Code Quality Tools

- **ESLint**: Configured with TypeScript rules and DEBUG logging
- **Prettier**: Formats code, SQL, and Tailwind classes
- **Husky + lint-staged**: Pre-commit hooks for formatting
- **Vitest**: Testing framework (both apps)

### Coding Style Rules

- **No code comments**: Code should be self-documenting
- **No redundant empty lines**: Keep code compact
- **No empty lines inside functions**: Function bodies should be tight
- **No empty lines inside JSX**: JSX blocks should be tight
- **Prefer arrow functions**: Use arrow functions over function declarations
- **No redundant return types**: Don't add return types that TypeScript can infer
- **Never attempt to start the app**: Don't run start/dev scripts - the user handles running the application
- **STRICT COMPLIANCE REQUIRED**: All agents must strictly follow these rules without exception

### Agent Instructions

- **Note/Remember Keywords**: When the user uses "note" or "remember" in their request, the agent should update this instructions file to capture the new information or requirement for future reference
- **Instructions File Sync**: When updating instructions/rules, these 3 files must be updated together with identical content: `.github/copilot-instructions.md`, `.cursor/rules/general.md`, `.trae/rules/project_rules.md`

### Special Files

- `generate_gs1.mjs`: GS1 barcode generation utility
- `optimize-imports.mjs`: Custom import organization script
- `apps/api/src/utils/seedDatabase.ts`: Database seeding logic
- `apps/web-ui/src/constants.ts`: Shared constants including paths

## Key Integration Points

### Database Transactions

Use `runInTransaction()` utility for complex operations that span multiple tables, especially for invoice creation and inventory updates.

### Error Handling

- tRPC errors use `TRPCError` with specific codes (`UNAUTHORIZED`, etc.)
- Frontend shows loading states and handles errors gracefully

### Multi-tenant Data Access

Always filter by `company_id` when querying data. The `assertAccess()` utility helps ensure proper scoping.
