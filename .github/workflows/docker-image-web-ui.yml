name: Build Docker Image for Web UI

env:
  IMAGE: ghcr.io/${{github.repository}}/web-ui:${{github.ref_name}}

on:
  push:
    paths:
      - apps/web-ui/**

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: cp package-lock.json apps/web-ui
      - run: docker login -u ${{github.repository_owner}} -p ${{secrets.GITHUB_TOKEN}} ghcr.io
      - name: Set build info
        id: buildinfo
        run: |
          echo "BUILD_HASH=$(git rev-parse --short HEAD)" >> $GITHUB_ENV
          echo "BUILD_TIMESTAMP=$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_ENV
      - run: |
          docker build -f apps/web-ui/Dockerfile \
            --build-arg VITE_APP_BUILD_HASH=${{ env.BUILD_HASH }} \
            --build-arg VITE_APP_BUILD_TIMESTAMP=${{ env.BUILD_TIMESTAMP }} \
            -t ${{env.IMAGE}} .
      - run: docker push ${{env.IMAGE}}
