{"[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "editor.codeActionsOnSave": ["source.fixAll.eslint", "source.organizeImports"], "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.quickSuggestions": {"strings": "on"}, "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.experimental.classRegex": [["classed(?:\\.\\w*)?\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*)[\"'`]"]], "typescript.tsdk": "node_modules/typescript/lib"}