#!/usr/bin/env node

import { existsSync, readFileSync } from 'fs'
import { dirname, join, resolve } from 'path'
import { IndentationText, Project } from 'ts-morph'
import { fileURLToPath } from 'url'

const __dirname = dirname(fileURLToPath(import.meta.url))

async function optimizeWorkspace(workspacePath, workspaceName) {
  console.info(`Optimizing imports in ${workspaceName}`)
  const tsConfigPath = join(workspacePath, 'tsconfig.json')
  const project = new Project({
    tsConfigFilePath: existsSync(tsConfigPath) ? tsConfigPath : undefined,
    useInMemoryFileSystem: false,
    manipulationSettings: { indentationText: IndentationText.TwoSpaces },
  })
  project.addSourceFilesAtPaths([
    join(workspacePath, 'src/**/*.{ts,tsx,js,jsx}'),
  ])
  let modifiedCount = 0
  for (const sourceFile of project.getSourceFiles()) {
    const originalText = sourceFile.getFullText()
    sourceFile.organizeImports()
    if (sourceFile.getFullText() !== originalText) {
      modifiedCount++
    }
  }
  if (modifiedCount > 0) {
    await project.save()
    console.info(`${modifiedCount} files processed.`)
  }
  return modifiedCount
}

const rootPackage = JSON.parse(
  readFileSync(join(__dirname, 'package.json'), 'utf-8'),
)
let totalModified = 0

for (const workspace of rootPackage.workspaces) {
  const workspacePath = resolve(__dirname, workspace)
  const workspaceName = workspace.split('/').pop()
  totalModified += await optimizeWorkspace(workspacePath, workspaceName)
}

console.info(`${totalModified} total files processed.`)
