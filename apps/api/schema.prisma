generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum PaymentAccountType {
  bank
  cash
}

enum PaymentDirection {
  incoming
  outgoing
}

enum PriceType {
  purchase
  sales
}

model BusinessParty {
  id          Int     @id @default(autoincrement())
  company_id  Int
  name        String
  is_supplier Boolean
  is_customer Boolean

  company Company @relation("business_parties__company_id", fields: [company_id], references: [id])

  business_party_inventories BusinessPartyInventory[] @relation("business_party_inventories__business_party_id")
  money_transactions         MoneyTransaction[]       @relation("money_transactions__business_party_id")
  payments                   Payment[]                @relation("payments__business_party_id")
  purchase_invoices          PurchaseInvoice[]        @relation("purchase_invoices__supplier_id")
  sales_invoices             SalesInvoice[]           @relation("sales_invoices__customer_id")

  @@index([company_id])
  @@index([company_id, is_supplier])
  @@index([company_id, is_customer])
  @@map("business_parties")
}

model BusinessPartyInventory {
  id                Int     @id @default(autoincrement())
  company_id        Int
  business_party_id Int
  amount            Decimal @db.Decimal(15, 2)

  company        Company       @relation("business_party_inventories__company_id", fields: [company_id], references: [id])
  business_party BusinessParty @relation("business_party_inventories__business_party_id", fields: [business_party_id], references: [id])

  @@unique([company_id, business_party_id])
  @@index([business_party_id])
  @@map("business_party_inventories")
}

model Company {
  id   Int    @id @default(autoincrement())
  name String

  business_parties           BusinessParty[]          @relation("business_parties__company_id")
  business_party_inventories BusinessPartyInventory[] @relation("business_party_inventories__company_id")
  datamatrix_requests        DatamatrixRequest[]      @relation("datamatrix_requests__company_id")
  datamatrix_responses       DatamatrixResponse[]     @relation("datamatrix_responses__company_id")
  money_transactions         MoneyTransaction[]       @relation("money_transactions__company_id")
  payment_accounts           PaymentAccount[]         @relation("payment_accounts__company_id")
  payments                   Payment[]                @relation("payments__company_id")
  permissions                Permission[]             @relation("permissions__company_id")
  price_adjustments          PriceAdjustment[]        @relation("price_adjustments__company_id")
  prices                     Price[]                  @relation("prices__company_id")
  product_inventories        ProductInventory[]       @relation("product_inventories__company_id")
  product_stocks             ProductStock[]           @relation("product_stocks__company_id")
  product_transactions       ProductTransaction[]     @relation("product_transactions__company_id")
  product_units              ProductUnit[]            @relation("product_units__company_id")
  products                   Product[]                @relation("products__company_id")
  purchase_invoices          PurchaseInvoice[]        @relation("purchase_invoices__company_id")
  sales_invoices             SalesInvoice[]           @relation("sales_invoices__company_id")
  warehouse_transfers        WarehouseTransfer[]      @relation("warehouse_transfers__company_id")
  warehouses                 Warehouse[]              @relation("warehouses__company_id")

  @@map("companies")
}

model DatamatrixRequest {
  id         Int @id @default(autoincrement())
  company_id Int

  company Company @relation("datamatrix_requests__company_id", fields: [company_id], references: [id])

  items DatamatrixRequestItem[] @relation("datamatrix_request_items__request_id")

  @@index([company_id])
  @@map("datamatrix_requests")
}

model DatamatrixRequestItem {
  id              Int @id @default(autoincrement())
  request_id      Int @map("datamatrix_request_id")
  quantity        Int
  product_unit_id Int

  request      DatamatrixRequest @relation("datamatrix_request_items__request_id", fields: [request_id], references: [id])
  product_unit ProductUnit       @relation("datamatrix_request_items__product_unit_id", fields: [product_unit_id], references: [id])

  @@index([request_id])
  @@index([product_unit_id])
  @@map("datamatrix_request_items")
}

model DatamatrixResponse {
  id         Int @id @default(autoincrement())
  company_id Int

  company Company @relation("datamatrix_responses__company_id", fields: [company_id], references: [id])

  items DatamatrixResponseItem[] @relation("datamatrix_response_items__response_id")

  @@index([company_id])
  @@map("datamatrix_responses")
}

model DatamatrixResponseItem {
  id              Int    @id @default(autoincrement())
  response_id     Int    @map("datamatrix_response_id")
  container_id    Int?
  product_unit_id Int
  datamatrix      String @unique

  response     DatamatrixResponse      @relation("datamatrix_response_items__response_id", fields: [response_id], references: [id])
  container    DatamatrixResponseItem? @relation("datamatrix_response_items__container_id", fields: [container_id], references: [id])
  product_unit ProductUnit             @relation("datamatrix_response_items__product_unit_id", fields: [product_unit_id], references: [id])

  children DatamatrixResponseItem[] @relation("datamatrix_response_items__container_id")

  @@index([response_id])
  @@index([container_id])
  @@index([product_unit_id])
  @@map("datamatrix_response_items")
}

model MoneyTransaction {
  id                  Int      @id @default(autoincrement())
  company_id          Int
  business_party_id   Int
  payment_account_id  Int?
  amount              Int
  date                DateTime @db.Date
  payment_id          Int?
  purchase_invoice_id Int?
  sales_invoice_id    Int?

  company          Company          @relation("money_transactions__company_id", fields: [company_id], references: [id])
  business_party   BusinessParty    @relation("money_transactions__business_party_id", fields: [business_party_id], references: [id])
  payment_account  PaymentAccount?  @relation("money_transactions__payment_account_id", fields: [payment_account_id], references: [id])
  payment          Payment?         @relation("money_transactions__payment_id", fields: [payment_id], references: [id])
  purchase_invoice PurchaseInvoice? @relation("money_transactions__purchase_invoice_id", fields: [purchase_invoice_id], references: [id])
  sales_invoice    SalesInvoice?    @relation("money_transactions__sales_invoice_id", fields: [sales_invoice_id], references: [id])

  @@index([company_id, date])
  @@index([business_party_id])
  @@index([payment_account_id])
  @@index([payment_id])
  @@index([purchase_invoice_id])
  @@index([sales_invoice_id])
  @@map("money_transactions")
}

model Payment {
  id                 Int      @id @default(autoincrement())
  company_id         Int
  business_party_id  Int
  payment_account_id Int?
  amount             Int
  date               DateTime @db.Date

  direction PaymentDirection

  company         Company         @relation("payments__company_id", fields: [company_id], references: [id])
  business_party  BusinessParty   @relation("payments__business_party_id", fields: [business_party_id], references: [id])
  payment_account PaymentAccount? @relation("payments__payment_account_id", fields: [payment_account_id], references: [id])

  money_transactions MoneyTransaction[] @relation("money_transactions__payment_id")

  @@index([company_id, date])
  @@index([business_party_id])
  @@index([payment_account_id])
  @@map("payments")
}

model PaymentAccount {
  id         Int                @id @default(autoincrement())
  company_id Int
  name       String
  type       PaymentAccountType

  company Company @relation("payment_accounts__company_id", fields: [company_id], references: [id])

  money_transactions MoneyTransaction[] @relation("money_transactions__payment_account_id")
  payments           Payment[]          @relation("payments__payment_account_id")

  @@index([company_id])
  @@map("payment_accounts")
}

model Permission {
  id         Int @id @default(autoincrement())
  company_id Int
  user_id    Int

  company Company @relation("permissions__company_id", fields: [company_id], references: [id])
  user    User    @relation("permissions__user_id", fields: [user_id], references: [id])

  @@unique([company_id, user_id])
  @@index([user_id])
  @@map("permissions")
}

model Price {
  id         Int       @id @default(autoincrement())
  company_id Int
  name       String
  type       PriceType

  company Company @relation("prices__company_id", fields: [company_id], references: [id])

  @@index([company_id])
  @@map("prices")
}

model Product {
  id         Int    @id @default(autoincrement())
  company_id Int
  name       String

  company Company @relation("products__company_id", fields: [company_id], references: [id])

  product_units ProductUnit[] @relation("product_units__product_id")

  @@index([company_id])
  @@map("products")
}

model ProductInventory {
  id           Int      @id @default(autoincrement())
  company_id   Int
  warehouse_id Int
  date         DateTime @db.Date
  total        Int

  company   Company   @relation("product_inventories__company_id", fields: [company_id], references: [id])
  warehouse Warehouse @relation("product_inventories__warehouse_id", fields: [warehouse_id], references: [id])

  items ProductInventoryItem[] @relation("product_inventory_items__inventory_id")

  @@index([company_id, date])
  @@index([warehouse_id])
  @@map("product_inventories")
}

model ProductInventoryItem {
  id              Int @id @default(autoincrement())
  inventory_id    Int
  product_unit_id Int
  amount          Int
  price           Int

  inventory    ProductInventory @relation("product_inventory_items__inventory_id", fields: [inventory_id], references: [id])
  product_unit ProductUnit      @relation("product_inventory_items__product_unit_id", fields: [product_unit_id], references: [id])

  product_transactions ProductTransaction[] @relation("product_transactions__product_inventory_item_id")

  @@index([inventory_id])
  @@index([product_unit_id])
  @@map("product_inventory_items")
}

model ProductStock {
  id              Int @id @default(autoincrement())
  company_id      Int
  warehouse_id    Int
  product_unit_id Int
  quantity        Int

  company      Company     @relation("product_stocks__company_id", fields: [company_id], references: [id])
  warehouse    Warehouse   @relation("product_stocks__warehouse_id", fields: [warehouse_id], references: [id])
  product_unit ProductUnit @relation("product_stocks__product_unit_id", fields: [product_unit_id], references: [id])

  @@unique([company_id, warehouse_id, product_unit_id])
  @@map("product_stocks")
}

model ProductTransaction {
  id                         Int      @id @default(autoincrement())
  company_id                 Int
  warehouse_id               Int
  product_unit_id            Int
  quantity                   Int
  price                      Int
  date                       DateTime @db.Date
  product_inventory_item_id  Int?
  purchase_invoice_item_id   Int?
  sales_invoice_item_id      Int?
  warehouse_transfer_item_id Int?

  company                 Company                @relation("product_transactions__company_id", fields: [company_id], references: [id])
  warehouse               Warehouse              @relation("product_transactions__warehouse_id", fields: [warehouse_id], references: [id])
  product_unit            ProductUnit            @relation("product_transactions__product_unit_id", fields: [product_unit_id], references: [id])
  product_inventory_item  ProductInventoryItem?  @relation("product_transactions__product_inventory_item_id", fields: [product_inventory_item_id], references: [id])
  purchase_invoice_item   PurchaseInvoiceItem?   @relation("product_transactions__purchase_invoice_item_id", fields: [purchase_invoice_item_id], references: [id])
  sales_invoice_item      SalesInvoiceItem?      @relation("product_transactions__sales_invoice_item_id", fields: [sales_invoice_item_id], references: [id])
  warehouse_transfer_item WarehouseTransferItem? @relation("product_transactions__warehouse_transfer_item_id", fields: [warehouse_transfer_item_id], references: [id])

  @@index([company_id, date])
  @@index([warehouse_id])
  @@index([product_unit_id])
  @@index([product_inventory_item_id])
  @@index([purchase_invoice_item_id])
  @@index([sales_invoice_item_id])
  @@index([warehouse_transfer_item_id])
  @@map("product_transactions")
}

model ProductUnit {
  id         Int    @id @default(autoincrement())
  company_id Int
  product_id Int
  parent_id  Int?
  name       String
  multiplier Int
  barcode    String

  company Company      @relation("product_units__company_id", fields: [company_id], references: [id])
  product Product      @relation("product_units__product_id", fields: [product_id], references: [id])
  parent  ProductUnit? @relation("product_units__parent_id", fields: [parent_id], references: [id])

  children                  ProductUnit[]            @relation("product_units__parent_id")
  datamatrix_request_items  DatamatrixRequestItem[]  @relation("datamatrix_request_items__product_unit_id")
  datamatrix_response_items DatamatrixResponseItem[] @relation("datamatrix_response_items__product_unit_id")
  price_adjustment_items    PriceAdjustmentItem[]    @relation("price_adjustment_items__product_unit_id")
  product_inventory_items   ProductInventoryItem[]   @relation("product_inventory_items__product_unit_id")
  product_stocks            ProductStock[]           @relation("product_stocks__product_unit_id")
  product_transactions      ProductTransaction[]     @relation("product_transactions__product_unit_id")
  purchase_invoice_items    PurchaseInvoiceItem[]    @relation("purchase_invoice_items__product_unit_id")
  sales_invoice_items       SalesInvoiceItem[]       @relation("sales_invoice_items__product_unit_id")
  warehouse_transfer_items  WarehouseTransferItem[]  @relation("warehouse_transfer_items__product_unit_id")

  @@unique([company_id, barcode])
  @@index([company_id, product_id])
  @@index([parent_id])
  @@map("product_units")
}

model PurchaseInvoice {
  id           Int      @id @default(autoincrement())
  company_id   Int
  supplier_id  Int
  warehouse_id Int
  date         DateTime @db.Date
  total        Int

  company   Company       @relation("purchase_invoices__company_id", fields: [company_id], references: [id])
  supplier  BusinessParty @relation("purchase_invoices__supplier_id", fields: [supplier_id], references: [id])
  warehouse Warehouse     @relation("purchase_invoices__warehouse_id", fields: [warehouse_id], references: [id])

  items              PurchaseInvoiceItem[] @relation("purchase_invoice_items__invoice_id")
  money_transactions MoneyTransaction[]    @relation("money_transactions__purchase_invoice_id")

  @@index([company_id, date])
  @@index([supplier_id])
  @@index([warehouse_id])
  @@map("purchase_invoices")
}

model PurchaseInvoiceItem {
  id              Int @id @default(autoincrement())
  invoice_id      Int
  product_unit_id Int
  amount          Int
  price           Int

  invoice      PurchaseInvoice @relation("purchase_invoice_items__invoice_id", fields: [invoice_id], references: [id])
  product_unit ProductUnit     @relation("purchase_invoice_items__product_unit_id", fields: [product_unit_id], references: [id])

  product_transactions ProductTransaction[] @relation("product_transactions__purchase_invoice_item_id")

  @@index([invoice_id])
  @@index([product_unit_id])
  @@map("purchase_invoice_items")
}

model SalesInvoice {
  id           Int      @id @default(autoincrement())
  company_id   Int
  customer_id  Int
  warehouse_id Int
  date         DateTime @db.Date
  total        Int

  company   Company       @relation("sales_invoices__company_id", fields: [company_id], references: [id])
  customer  BusinessParty @relation("sales_invoices__customer_id", fields: [customer_id], references: [id])
  warehouse Warehouse     @relation("sales_invoices__warehouse_id", fields: [warehouse_id], references: [id])

  items              SalesInvoiceItem[] @relation("sales_invoice_items__invoice_id")
  money_transactions MoneyTransaction[] @relation("money_transactions__sales_invoice_id")

  @@index([company_id, date])
  @@index([customer_id])
  @@index([warehouse_id])
  @@map("sales_invoices")
}

model SalesInvoiceItem {
  id              Int @id @default(autoincrement())
  invoice_id      Int
  product_unit_id Int
  amount          Int
  price           Int

  invoice      SalesInvoice @relation("sales_invoice_items__invoice_id", fields: [invoice_id], references: [id])
  product_unit ProductUnit  @relation("sales_invoice_items__product_unit_id", fields: [product_unit_id], references: [id])

  product_transactions ProductTransaction[] @relation("product_transactions__sales_invoice_item_id")

  @@index([invoice_id])
  @@index([product_unit_id])
  @@map("sales_invoice_items")
}

model Token {
  id      Int    @id @default(autoincrement())
  user_id Int
  value   String @unique

  user User @relation("tokens__user_id", fields: [user_id], references: [id])

  @@index([user_id])
  @@map("tokens")
}

model User {
  id        Int     @id @default(autoincrement())
  is_active Boolean
  email     String  @unique
  password  String

  permissions Permission[] @relation("permissions__user_id")
  tokens      Token[]      @relation("tokens__user_id")

  @@map("users")
}

model Warehouse {
  id         Int    @id @default(autoincrement())
  company_id Int
  name       String

  company Company @relation("warehouses__company_id", fields: [company_id], references: [id])

  product_inventories      ProductInventory[]   @relation("product_inventories__warehouse_id")
  product_stocks           ProductStock[]       @relation("product_stocks__warehouse_id")
  product_transactions     ProductTransaction[] @relation("product_transactions__warehouse_id")
  purchase_invoices        PurchaseInvoice[]    @relation("purchase_invoices__warehouse_id")
  sales_invoices           SalesInvoice[]       @relation("sales_invoices__warehouse_id")
  warehouse_transfers_from WarehouseTransfer[]  @relation("warehouse_transfers__from_warehouse_id")
  warehouse_transfers_to   WarehouseTransfer[]  @relation("warehouse_transfers__to_warehouse_id")

  @@index([company_id])
  @@map("warehouses")
}

model WarehouseTransfer {
  id                Int      @id @default(autoincrement())
  company_id        Int
  from_warehouse_id Int
  to_warehouse_id   Int
  date              DateTime @db.Date
  total             Int

  company        Company   @relation("warehouse_transfers__company_id", fields: [company_id], references: [id])
  from_warehouse Warehouse @relation("warehouse_transfers__from_warehouse_id", fields: [from_warehouse_id], references: [id])
  to_warehouse   Warehouse @relation("warehouse_transfers__to_warehouse_id", fields: [to_warehouse_id], references: [id])

  items WarehouseTransferItem[] @relation("warehouse_transfer_items__transfer_id")

  @@index([company_id, date])
  @@index([from_warehouse_id])
  @@index([to_warehouse_id])
  @@map("warehouse_transfers")
}

model WarehouseTransferItem {
  id              Int @id @default(autoincrement())
  transfer_id     Int
  product_unit_id Int
  amount          Int
  price           Int

  transfer     WarehouseTransfer @relation("warehouse_transfer_items__transfer_id", fields: [transfer_id], references: [id])
  product_unit ProductUnit       @relation("warehouse_transfer_items__product_unit_id", fields: [product_unit_id], references: [id])

  product_transactions ProductTransaction[] @relation("product_transactions__warehouse_transfer_item_id")

  @@index([transfer_id])
  @@index([product_unit_id])
  @@map("warehouse_transfer_items")
}

model PriceAdjustment {
  id         Int      @id @default(autoincrement())
  company_id Int
  date       DateTime @db.Date

  company Company @relation("price_adjustments__company_id", fields: [company_id], references: [id])

  items PriceAdjustmentItem[] @relation("price_adjustment_items__adjustment_id")

  @@index([company_id, date])
  @@map("price_adjustments")
}

model PriceAdjustmentItem {
  id              Int @id @default(autoincrement())
  adjustment_id   Int
  product_unit_id Int
  price           Int

  adjustment   PriceAdjustment @relation("price_adjustment_items__adjustment_id", fields: [adjustment_id], references: [id], onDelete: Cascade)
  product_unit ProductUnit     @relation("price_adjustment_items__product_unit_id", fields: [product_unit_id], references: [id])

  @@index([adjustment_id])
  @@index([product_unit_id])
  @@map("price_adjustment_items")
}
