FROM node:22 AS base
WORKDIR /app

FROM base AS dev
COPY . ./
RUN npm ci
RUN npm run build --workspace=@cloudretail/api

FROM base AS prod
COPY apps/api/package.json ./
COPY package-lock.json ./
COPY apps/api/schema.prisma ./
RUN npm ci --omit=dev

FROM base
COPY --from=prod /app/node_modules ./node_modules
COPY --from=dev /app/apps/api/dist ./src
COPY --from=dev /app/package-lock.json ./
COPY /apps/api/package.json ./
COPY /apps/api/schema.prisma ./
COPY /apps/api/migrations ./migrations
CMD ["node", "src/index.js"]
