import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, isoStringToDate } from '../../utils/index.js'

export const findMany = protectedProcedure
  .input(z.object({ product_id: z.number(), company_id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    const transactions = await prisma.productTransaction.findMany({
      where: {
        company_id: input.company_id,
        product_unit: { product_id: input.product_id },
      },
      include: {
        warehouse: true,
        product_unit: { include: { product: true } },
      },
      orderBy: { date: 'desc' },
    })
    return transactions.map(transaction => ({
      ...transaction,
      date: isoStringToDate(transaction.date.toISOString()),
    }))
  })
