import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const createOne = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      business_party_id: z.number(),
      amount: z.number(),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.businessPartyInventory.create({
      data: {
        company_id: input.company_id,
        business_party_id: input.business_party_id,
        amount: input.amount,
      },
      include: { business_party: true },
    })
  })
