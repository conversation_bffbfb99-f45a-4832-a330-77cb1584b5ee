import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(z.object({ id: z.number(), amount: z.number().optional() }))
  .mutation(async ({ ctx, input }) => {
    const existingInventory =
      await prisma.businessPartyInventory.findUniqueOrThrow({
        where: { id: input.id },
        select: { company_id: true },
      })
    await assertAccess(existingInventory.company_id, ctx.user.id, prisma)
    return prisma.businessPartyInventory.update({
      where: { id: input.id },
      data: { amount: input.amount },
      include: { business_party: true },
    })
  })
