import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const deleteInventory = protectedProcedure
  .input(z.object({ id: z.number() }))
  .mutation(async ({ ctx, input }) => {
    const existingInventory =
      await prisma.businessPartyInventory.findUniqueOrThrow({
        where: { id: input.id },
        select: { company_id: true },
      })
    await assertAccess(existingInventory.company_id, ctx.user.id, prisma)
    return prisma.businessPartyInventory.delete({ where: { id: input.id } })
  })
