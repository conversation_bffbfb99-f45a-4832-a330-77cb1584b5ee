import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number() }))
  .query(async ({ ctx, input }) => {
    const businessPartyInventory =
      await prisma.businessPartyInventory.findUniqueOrThrow({
        where: { id: input.id },
        include: { business_party: true, company: true },
      })
    await assertAccess(businessPartyInventory.company_id, ctx.user.id, prisma)
    return businessPartyInventory
  })
