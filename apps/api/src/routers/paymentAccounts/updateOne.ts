import { PaymentAccountType } from '@prisma/client'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(
    z.object({
      id: z.number(),
      name: z.string(),
      type: z.enum(PaymentAccountType),
      company_id: z.number(),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.paymentAccount.update({
      where: { id: input.id },
      data: { name: input.name, type: input.type },
    })
  })
