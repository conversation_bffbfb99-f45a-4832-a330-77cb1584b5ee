import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findMany = protectedProcedure
  .input(z.object({ company_id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.paymentAccount.findMany({
      where: { company_id: input.company_id },
    })
  })
