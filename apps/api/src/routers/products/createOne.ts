import assert from 'node:assert'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const createOne = protectedProcedure
  .input(
    z.object({
      name: z.string(),
      company_id: z.number(),
      product_units: z
        .array(
          z.object({
            name: z.string(),
            multiplier: z.number().positive(),
            barcode: z.string(),
            parent_id: z.number().nullable().optional(),
          }),
        )
        .min(1),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.$transaction(async prisma => {
      const product = await prisma.product.create({
        data: { name: input.name, company_id: input.company_id },
      })
      for (const unit of input.product_units) {
        if (unit.parent_id) {
          const parent = await prisma.productUnit.findUniqueOrThrow({
            where: { id: unit.parent_id },
          })
          assert.equal(input.company_id, parent.company_id)
        }
        await prisma.productUnit.create({
          data: {
            product_id: product.id,
            company_id: input.company_id,
            parent_id: unit.parent_id,
            name: unit.name,
            multiplier: unit.multiplier,
            barcode: unit.barcode,
          },
        })
      }
      return product
    })
  })
