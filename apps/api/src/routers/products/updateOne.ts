import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(
    z.object({
      id: z.number(),
      name: z.string(),
      product_units: z
        .array(
          z.object({
            id: z.number().nullable().optional(),
            name: z.string(),
            multiplier: z.number().positive(),
            barcode: z.string(),
            parent_id: z.number().nullable().optional(),
          }),
        )
        .min(1),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    const product = await prisma.product.findUniqueOrThrow({
      where: { id: input.id },
    })
    await assertAccess(product.company_id, ctx.user.id, prisma)
    return prisma.$transaction(async prisma => {
      const updatedProduct = await prisma.product.update({
        where: { id: input.id },
        data: { name: input.name },
      })

      const existingUnits = await prisma.productUnit.findMany({
        where: { product_id: input.id },
      })

      const newUnits = input.product_units.filter(unit => !unit.id)
      const updatedUnits = input.product_units.filter(unit => unit.id)

      const updatedUnitIds = new Set(updatedUnits.map(unit => unit.id!))

      const unitsToDelete = existingUnits.filter(
        unit => !updatedUnitIds.has(unit.id),
      )
      for (const unit of unitsToDelete) {
        await prisma.productUnit.delete({ where: { id: unit.id } })
      }

      for (const unit of updatedUnits) {
        await prisma.productUnit.update({
          where: { id: unit.id! },
          data: {
            name: unit.name,
            multiplier: unit.multiplier,
            barcode: unit.barcode,
            parent_id: unit.parent_id,
          },
        })
      }

      for (const unit of newUnits) {
        await prisma.productUnit.create({
          data: {
            product_id: input.id,
            company_id: product.company_id,
            parent_id: unit.parent_id,
            name: unit.name,
            multiplier: unit.multiplier,
            barcode: unit.barcode,
          },
        })
      }

      return updatedProduct
    })
  })
