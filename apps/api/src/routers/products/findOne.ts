import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number() }))
  .query(async ({ ctx, input }) => {
    const product = await prisma.product.findUniqueOrThrow({
      where: { id: input.id },
      include: { product_units: true },
    })
    await assertAccess(product.company_id, ctx.user.id, prisma)
    return product
  })
