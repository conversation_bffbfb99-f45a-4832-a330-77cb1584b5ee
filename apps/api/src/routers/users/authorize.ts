import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const authorize = protectedProcedure
  .input(z.object({ company_id: z.number(), email: z.email() }))
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    const targetUser = await prisma.user.findUniqueOrThrow({
      where: { email: input.email },
    })
    return prisma.permission.create({
      data: { company_id: input.company_id, user_id: targetUser.id },
    })
  })
