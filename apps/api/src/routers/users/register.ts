import nodemailer from 'nodemailer'
import { z } from 'zod'
import {
  MAIL_HOST,
  MAIL_PASSWORD,
  MAIL_PORT,
  MAIL_USER,
  VERIFICATION_URL_PREFIX,
} from '../../constants.js'
import { prisma } from '../../prisma.js'
import { procedure } from '../../trpc/procedures.js'
import { hashPassword } from '../../utils/hashPassword.js'

const transporter = nodemailer.createTransport({
  host: MAIL_HOST,
  port: parseInt(MAIL_PORT),
  secure: true,
  auth: { user: MAIL_USER, pass: MAIL_PASSWORD },
})

export const register = procedure
  .input(z.object({ email: z.email(), password: z.string().min(6) }))
  .mutation(async ({ input }) => {
    const user = await prisma.user.create({
      data: {
        email: input.email,
        password: hashPassword(input.password),
        is_active: false,
      },
    })

    const inputData = { id: user.id }
    const encodedInput = encodeURIComponent(JSON.stringify(inputData))
    const url = `${VERIFICATION_URL_PREFIX}/trpc/users.activate?input=${encodedInput}`

    await transporter.sendMail({
      from: MAIL_USER,
      to: input.email,
      subject: 'Confirmation',
      html: `<a href="${url}">Please, confirm your registration</a>`,
    })

    return { id: user.id, email: user.email }
  })
