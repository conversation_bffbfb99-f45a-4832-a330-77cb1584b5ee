import { TRPCError } from '@trpc/server'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { procedure } from '../../trpc/procedures.js'

export const activate = procedure
  .input(z.object({ id: z.number() }))
  .query(async ({ input }) => {
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: input.id },
    })
    if (user.is_active) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: 'User already activated',
      })
    }
    return prisma.user.update({
      where: { id: input.id },
      data: { is_active: true },
    })
  })
