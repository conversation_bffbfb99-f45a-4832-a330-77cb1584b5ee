import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findItems = protectedProcedure
  .input(z.object({ datamatrix_request_id: z.number() }))
  .query(async ({ ctx, input }) => {
    const datamatrixRequest = await prisma.datamatrixRequest.findUniqueOrThrow({
      where: { id: input.datamatrix_request_id },
    })
    await assertAccess(datamatrixRequest.company_id, ctx.user.id, prisma)
    return prisma.datamatrixRequestItem.findMany({
      where: { request_id: input.datamatrix_request_id },
      include: { product_unit: { include: { product: true } } },
    })
  })
