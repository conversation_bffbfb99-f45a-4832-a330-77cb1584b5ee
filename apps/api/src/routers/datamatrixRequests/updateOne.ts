import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, mapAsyncSequential } from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(
    z.object({
      id: z.number(),
      items: z
        .array(
          z.object({
            id: z.number().optional(),
            product_unit_id: z.number(),
            quantity: z.number(),
          }),
        )
        .optional(),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    const datamatrixRequest = await prisma.datamatrixRequest.findUniqueOrThrow({
      where: { id: input.id },
    })
    await assertAccess(datamatrixRequest.company_id, ctx.user.id, prisma)
    if (input.items) {
      return prisma.$transaction(async prisma => {
        const existingItems = await prisma.datamatrixRequestItem.findMany({
          where: { request_id: input.id },
          select: { id: true },
        })
        const existingItemIds = existingItems.map(item => item.id)
        const inputItemsWithId = input.items!.filter(item => item.id)
        const inputItemsWithoutId = input.items!.filter(item => !item.id)
        const inputItemIds = inputItemsWithId.map(item => item.id!)
        const itemsToDelete = existingItemIds.filter(
          id => !inputItemIds.includes(id),
        )
        await prisma.datamatrixRequestItem.deleteMany({
          where: { id: { in: itemsToDelete } },
        })
        await mapAsyncSequential(inputItemsWithId, item =>
          prisma.datamatrixRequestItem.update({
            where: { id: item.id! },
            data: {
              product_unit_id: item.product_unit_id,
              quantity: item.quantity,
            },
          }),
        )
        await mapAsyncSequential(inputItemsWithoutId, item =>
          prisma.datamatrixRequestItem.create({
            data: {
              request_id: input.id,
              product_unit_id: item.product_unit_id,
              quantity: item.quantity,
            },
          }),
        )
        const updatedDatamatrixRequest = await prisma.datamatrixRequest.update({
          where: { id: input.id },
          data: {},
        })
        const items = await prisma.datamatrixRequestItem.findMany({
          where: { request_id: input.id },
        })
        return { ...updatedDatamatrixRequest, items }
      })
    } else {
      return prisma.datamatrixRequest.update({
        where: { id: input.id },
        data: {},
      })
    }
  })
