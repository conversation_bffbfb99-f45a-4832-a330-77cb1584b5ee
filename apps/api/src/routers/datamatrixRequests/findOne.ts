import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number() }))
  .query(async ({ ctx, input }) => {
    const datamatrixRequest = await prisma.datamatrixRequest.findUniqueOrThrow({
      where: { id: input.id },
    })
    await assertAccess(datamatrixRequest.company_id, ctx.user.id, prisma)
    const items = await prisma.datamatrixRequestItem.findMany({
      where: { request_id: input.id },
    })
    return { ...datamatrixRequest, items }
  })
