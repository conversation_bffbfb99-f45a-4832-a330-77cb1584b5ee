import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, mapAsyncSequential } from '../../utils/index.js'

export const createOne = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      items: z.array(
        z.object({ product_unit_id: z.number(), quantity: z.number() }),
      ),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.$transaction(async prisma => {
      const datamatrixRequest = await prisma.datamatrixRequest.create({
        data: { company_id: input.company_id },
      })
      const items = await mapAsyncSequential(input.items, item =>
        prisma.datamatrixRequestItem.create({
          data: {
            request_id: datamatrixRequest.id,
            product_unit_id: item.product_unit_id,
            quantity: item.quantity,
          },
        }),
      )
      return { ...datamatrixRequest, items }
    })
  })
