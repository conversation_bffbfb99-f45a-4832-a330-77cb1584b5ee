import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, getProductStock } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      warehouse_id: z.number(),
      product_unit_id: z.number(),
    }),
  )
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)

    return await prisma.$transaction(async prisma => {
      const quantity = await getProductStock(
        prisma,
        input.company_id,
        input.warehouse_id,
        input.product_unit_id,
      )

      const productUnit = await prisma.productUnit.findUniqueOrThrow({
        where: { id: input.product_unit_id },
        include: { product: true },
      })

      const warehouse = await prisma.warehouse.findUniqueOrThrow({
        where: { id: input.warehouse_id },
      })

      return {
        company_id: input.company_id,
        warehouse_id: input.warehouse_id,
        warehouse_name: warehouse.name,
        product_unit_id: input.product_unit_id,
        product_id: productUnit.product.id,
        product_name: productUnit.product.name,
        unit_name: productUnit.name,
        unit_barcode: productUnit.barcode,
        quantity,
      }
    })
  })
