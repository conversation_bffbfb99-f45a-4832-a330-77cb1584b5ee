import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, validateStockAvailability } from '../../utils/index.js'

export const validateAvailability = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      items: z.array(
        z.object({
          warehouse_id: z.number(),
          product_unit_id: z.number(),
          required_quantity: z.number().positive(),
        }),
      ),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)

    return await prisma.$transaction(async prisma => {
      const itemsWithCompanyId = input.items.map(item => ({
        ...item,
        company_id: input.company_id,
      }))

      try {
        await validateStockAvailability(prisma, itemsWithCompanyId)
        return { valid: true, message: 'All items have sufficient stock' }
      } catch (error) {
        return {
          valid: false,
          message:
            error instanceof Error ? error.message : 'Stock validation failed',
        }
      }
    })
  })
