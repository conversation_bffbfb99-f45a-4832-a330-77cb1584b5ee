import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  getWarehouseStocks as getWarehouseStocksUtil,
} from '../../utils/index.js'

export const getWarehouseStocks = protectedProcedure
  .input(z.object({ company_id: z.number(), warehouse_id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)

    return await prisma.$transaction(async prisma => {
      const stocks = await getWarehouseStocksUtil(
        prisma,
        input.company_id,
        input.warehouse_id,
      )

      const warehouse = await prisma.warehouse.findUniqueOrThrow({
        where: { id: input.warehouse_id },
      })

      return {
        warehouse_id: input.warehouse_id,
        warehouse_name: warehouse.name,
        stocks,
      }
    })
  })
