import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, reconcileProductStock } from '../../utils/index.js'

export const bulkReconcile = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      warehouse_id: z.number().optional(),
      product_id: z.number().optional(),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)

    return await prisma.$transaction(async prisma => {
      // Build the where clause for finding stocks to reconcile
      const whereClause: any = { company_id: input.company_id }

      if (input.warehouse_id) {
        whereClause.warehouse_id = input.warehouse_id
      }

      if (input.product_id) {
        whereClause.product_unit = { product_id: input.product_id }
      }

      // Get all stocks that match the criteria
      const stocks = await prisma.productStock.findMany({
        where: whereClause,
        include: {
          warehouse: true,
          product_unit: { include: { product: true } },
        },
      })

      const results = []
      let totalFixed = 0

      // Reconcile each stock
      for (const stock of stocks) {
        const result = await reconcileProductStock(
          prisma,
          stock.company_id,
          stock.warehouse_id,
          stock.product_unit_id,
        )

        if (result.fixed) {
          totalFixed++
        }

        results.push({
          warehouse_id: stock.warehouse_id,
          warehouse_name: stock.warehouse.name,
          product_unit_id: stock.product_unit_id,
          product_name: stock.product_unit.product.name,
          unit_name: stock.product_unit.name,
          ...result,
        })
      }

      return {
        total_processed: stocks.length,
        total_fixed: totalFixed,
        results,
      }
    })
  })
