import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const getLowStockReport = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      warehouse_id: z.number().optional(),
      threshold: z.number().default(10),
    }),
  )
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)

    const whereClause: any = {
      company_id: input.company_id,
      quantity: { lte: input.threshold, gt: 0 },
    }

    if (input.warehouse_id) {
      whereClause.warehouse_id = input.warehouse_id
    }

    const lowStockItems = await prisma.productStock.findMany({
      where: whereClause,
      include: {
        warehouse: true,
        product_unit: { include: { product: true } },
      },
      orderBy: [
        { quantity: 'asc' },
        { warehouse: { name: 'asc' } },
        { product_unit: { product: { name: 'asc' } } },
        { product_unit: { name: 'asc' } },
      ],
    })

    return {
      threshold: input.threshold,
      total_items: lowStockItems.length,
      items: lowStockItems.map(stock => ({
        warehouse_id: stock.warehouse_id,
        warehouse_name: stock.warehouse.name,
        product_unit_id: stock.product_unit_id,
        product_id: stock.product_unit.product.id,
        product_name: stock.product_unit.product.name,
        unit_name: stock.product_unit.name,
        unit_barcode: stock.product_unit.barcode,
        current_quantity: stock.quantity,
        status: stock.quantity === 0 ? 'out_of_stock' : 'low_stock',
      })),
    }
  })
