import { router } from '../../trpc/procedures.js'
import { adjustStock } from './adjustStock.js'
import { bulkReconcile } from './bulkReconcile.js'
import { findMany } from './findMany.js'
import { findOne } from './findOne.js'
import { getLowStockReport } from './getLowStockReport.js'
import { getWarehouseStocks } from './getWarehouseStocks.js'
import { reconcileStock } from './reconcileStock.js'
import { validateAvailability } from './validateAvailability.js'

export const productStocksRouter = router({
  findMany,
  findOne,
  getWarehouseStocks,
  validateAvailability,
  getLowStockReport,
  adjustStock,
  reconcileStock,
  bulkReconcile,
})
