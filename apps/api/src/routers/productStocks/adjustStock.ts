import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
  updateProductStock,
} from '../../utils/index.js'

export const adjustStock = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      warehouse_id: z.number(),
      product_unit_id: z.number(),
      quantity_adjustment: z.number(),
      reason: z.string().optional(),
      date: dateOnlyString.default(
        () => new Date().toISOString().split('T')[0],
      ),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)

    return await prisma.$transaction(async prisma => {
      // Get current stock before adjustment
      const currentStock = await prisma.productStock.findUnique({
        where: {
          company_id_warehouse_id_product_unit_id: {
            company_id: input.company_id,
            warehouse_id: input.warehouse_id,
            product_unit_id: input.product_unit_id,
          },
        },
        select: { quantity: true },
      })

      const currentQuantity = currentStock?.quantity || 0
      const newQuantity = currentQuantity + input.quantity_adjustment

      // Prevent negative stock
      if (newQuantity < 0) {
        throw new Error(
          `Stock adjustment would result in negative quantity. Current: ${currentQuantity}, Adjustment: ${input.quantity_adjustment}`,
        )
      }

      // Apply the stock adjustment
      await updateProductStock(prisma, {
        company_id: input.company_id,
        warehouse_id: input.warehouse_id,
        product_unit_id: input.product_unit_id,
        quantity: input.quantity_adjustment,
        date: new Date(dateToIsoString(input.date)),
      })

      // Create a transaction record for the adjustment
      await prisma.productTransaction.create({
        data: {
          company_id: input.company_id,
          warehouse_id: input.warehouse_id,
          product_unit_id: input.product_unit_id,
          quantity: input.quantity_adjustment,
          price: 0, // Stock adjustments don't have a price
          date: dateToIsoString(input.date),
          // Note: All foreign key fields are null for manual adjustments
        },
      })

      return {
        previous_quantity: currentQuantity,
        adjustment: input.quantity_adjustment,
        new_quantity: newQuantity,
        reason: input.reason,
        date: input.date,
      }
    })
  })
