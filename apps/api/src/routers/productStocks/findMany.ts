import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findMany = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      warehouse_id: z.number().optional(),
      product_id: z.number().optional(),
      product_unit_id: z.number().optional(),
      min_quantity: z.number().optional(),
      include_zero_stock: z.boolean().default(false),
    }),
  )
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)

    const whereClause: any = { company_id: input.company_id }

    if (input.warehouse_id) {
      whereClause.warehouse_id = input.warehouse_id
    }

    if (input.product_unit_id) {
      whereClause.product_unit_id = input.product_unit_id
    }

    if (input.product_id) {
      whereClause.product_unit = { product_id: input.product_id }
    }

    if (!input.include_zero_stock) {
      whereClause.quantity = { gt: 0 }
    }

    if (input.min_quantity !== undefined) {
      whereClause.quantity = {
        ...(whereClause.quantity || {}),
        gte: input.min_quantity,
      }
    }

    const stocks = await prisma.productStock.findMany({
      where: whereClause,
      include: {
        warehouse: true,
        product_unit: { include: { product: true } },
      },
      orderBy: [
        { warehouse: { name: 'asc' } },
        { product_unit: { product: { name: 'asc' } } },
        { product_unit: { name: 'asc' } },
      ],
    })

    return stocks.map(stock => ({
      id: stock.id,
      company_id: stock.company_id,
      warehouse_id: stock.warehouse_id,
      warehouse_name: stock.warehouse.name,
      product_unit_id: stock.product_unit_id,
      product_id: stock.product_unit.product.id,
      product_name: stock.product_unit.product.name,
      unit_name: stock.product_unit.name,
      unit_barcode: stock.product_unit.barcode,
      quantity: stock.quantity,
    }))
  })
