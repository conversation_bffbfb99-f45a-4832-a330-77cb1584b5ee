import assert from 'node:assert'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const setContainer = protectedProcedure
  .input(
    z.object({ container_datamatrix: z.string(), item_datamatrix: z.string() }),
  )
  .mutation(async ({ ctx, input }) => {
    const containerItem = await prisma.datamatrixResponseItem.findUniqueOrThrow(
      {
        where: { datamatrix: input.container_datamatrix },
        include: { response: true, product_unit: true },
      },
    )
    const itemToUpdate = await prisma.datamatrixResponseItem.findUniqueOrThrow({
      where: { datamatrix: input.item_datamatrix },
      include: { response: true },
    })
    await assertAccess(containerItem.response.company_id, ctx.user.id, prisma)
    await assertAccess(itemToUpdate.response.company_id, ctx.user.id, prisma)
    if (
      containerItem.response.company_id !== itemToUpdate.response.company_id
    ) {
      throw new Error('Container and item must belong to the same company')
    }
    const currentContainerItems = await prisma.datamatrixResponseItem.findMany({
      where: { container_id: containerItem.id },
    })
    assert(
      currentContainerItems.length < containerItem.product_unit.multiplier,
      'Container is full',
    )
    return prisma.datamatrixResponseItem.update({
      where: { id: itemToUpdate.id },
      data: { container_id: containerItem.id },
    })
  })
