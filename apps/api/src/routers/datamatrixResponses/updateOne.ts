import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, mapAsyncSequential } from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(
    z.object({
      id: z.number(),
      items: z
        .array(
          z.object({
            id: z.number().optional(),
            product_unit_id: z.number(),
            datamatrix: z.string(),
          }),
        )
        .optional(),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    const datamatrixResponse =
      await prisma.datamatrixResponse.findUniqueOrThrow({
        where: { id: input.id },
      })
    await assertAccess(datamatrixResponse.company_id, ctx.user.id, prisma)
    if (input.items) {
      return prisma.$transaction(async prisma => {
        const existingItems = await prisma.datamatrixResponseItem.findMany({
          where: { response_id: input.id },
          select: { id: true },
        })
        const existingItemIds = existingItems.map(item => item.id)
        const inputItemsWithId = input.items!.filter(item => item.id)
        const inputItemsWithoutId = input.items!.filter(item => !item.id)
        const inputItemIds = inputItemsWithId.map(item => item.id!)
        const itemsToDelete = existingItemIds.filter(
          id => !inputItemIds.includes(id),
        )
        await prisma.datamatrixResponseItem.deleteMany({
          where: { id: { in: itemsToDelete } },
        })
        await mapAsyncSequential(inputItemsWithId, item =>
          prisma.datamatrixResponseItem.update({
            where: { id: item.id! },
            data: {
              product_unit_id: item.product_unit_id,
              datamatrix: item.datamatrix,
            },
          }),
        )
        await mapAsyncSequential(inputItemsWithoutId, item =>
          prisma.datamatrixResponseItem.create({
            data: {
              response_id: input.id,
              product_unit_id: item.product_unit_id,
              datamatrix: item.datamatrix,
            },
          }),
        )
        const updatedDatamatrixResponse =
          await prisma.datamatrixResponse.update({
            where: { id: input.id },
            data: {},
          })
        const items = await prisma.datamatrixResponseItem.findMany({
          where: { response_id: input.id },
        })
        return { ...updatedDatamatrixResponse, items }
      })
    } else {
      return prisma.datamatrixResponse.update({
        where: { id: input.id },
        data: {},
      })
    }
  })
