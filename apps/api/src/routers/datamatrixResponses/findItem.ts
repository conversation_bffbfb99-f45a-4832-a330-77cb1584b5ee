import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findItem = protectedProcedure
  .input(z.object({ datamatrix: z.string() }))
  .query(async ({ ctx, input }) => {
    const datamatrixResponseItem =
      await prisma.datamatrixResponseItem.findUniqueOrThrow({
        where: { datamatrix: input.datamatrix },
        include: {
          response: true,
          product_unit: { include: { product: true } },
        },
      })
    await assertAccess(
      datamatrixResponseItem.response.company_id,
      ctx.user.id,
      prisma,
    )
    return {
      ...datamatrixResponseItem,
      product_unit: datamatrixResponseItem.product_unit,
    }
  })
