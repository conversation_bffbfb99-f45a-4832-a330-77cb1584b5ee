import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number() }))
  .query(async ({ ctx, input }) => {
    const datamatrixResponse =
      await prisma.datamatrixResponse.findUniqueOrThrow({
        where: { id: input.id },
      })
    await assertAccess(datamatrixResponse.company_id, ctx.user.id, prisma)
    const items = await prisma.datamatrixResponseItem.findMany({
      where: { response_id: input.id },
    })
    return { ...datamatrixResponse, items }
  })
