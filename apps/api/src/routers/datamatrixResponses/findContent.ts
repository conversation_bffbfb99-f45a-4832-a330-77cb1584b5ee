import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findContent = protectedProcedure
  .input(z.object({ id: z.number() }))
  .query(async ({ ctx, input }) => {
    const datamatrixResponseItem =
      await prisma.datamatrixResponseItem.findUniqueOrThrow({
        where: { id: input.id },
        include: { response: true },
      })
    await assertAccess(
      datamatrixResponseItem.response.company_id,
      ctx.user.id,
      prisma,
    )
    return prisma.datamatrixResponseItem.findMany({
      where: { container_id: input.id },
      include: { product_unit: { include: { product: true } } },
    })
  })
