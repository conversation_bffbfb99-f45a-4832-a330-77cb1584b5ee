import { router } from '../../trpc/procedures.js'
import { createOne } from './createOne.js'
import { findContent } from './findContent.js'
import { findItem } from './findItem.js'
import { findItems } from './findItems.js'
import { findMany } from './findMany.js'
import { findOne } from './findOne.js'
import { setContainer } from './setContainer.js'
import { updateOne } from './updateOne.js'

export const datamatrixResponsesRouter = router({
  createOne,
  findContent,
  findItem,
  findItems,
  findMany,
  findOne,
  setContainer,
  updateOne,
})
