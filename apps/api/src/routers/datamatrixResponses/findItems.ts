import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findItems = protectedProcedure
  .input(z.object({ datamatrix_response_id: z.number() }))
  .query(async ({ ctx, input }) => {
    const datamatrixResponse =
      await prisma.datamatrixResponse.findUniqueOrThrow({
        where: { id: input.datamatrix_response_id },
      })
    await assertAccess(datamatrixResponse.company_id, ctx.user.id, prisma)
    return prisma.datamatrixResponseItem.findMany({
      where: { response_id: input.datamatrix_response_id },
      include: { product_unit: { include: { product: true } } },
    })
  })
