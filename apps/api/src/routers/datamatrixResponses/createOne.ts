import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, mapAsyncSequential } from '../../utils/index.js'

export const createOne = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      items: z.array(
        z.object({ product_unit_id: z.number(), datamatrix: z.string() }),
      ),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.$transaction(async prisma => {
      const datamatrixResponse = await prisma.datamatrixResponse.create({
        data: { company_id: input.company_id },
      })
      const items = await mapAsyncSequential(input.items, item =>
        prisma.datamatrixResponseItem.create({
          data: {
            response_id: datamatrixResponse.id,
            product_unit_id: item.product_unit_id,
            datamatrix: item.datamatrix,
          },
        }),
      )
      return { ...datamatrixResponse, items }
    })
  })
