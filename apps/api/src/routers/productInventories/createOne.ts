import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
  isoStringToDate,
  mapAsyncSequential,
  syncProductInventoryItemTransactions,
} from '../../utils/index.js'

export const createOne = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      warehouse_id: z.number(),
      date: dateOnlyString,
      items: z.array(
        z.object({
          product_unit_id: z.number(),
          amount: z.number().positive(),
          price: z.number().int().positive(),
        }),
      ),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return await prisma.$transaction(async prisma => {
      const total = input.items.reduce(
        (sum, item) => sum + item.amount * item.price,
        0,
      )
      const productInventory = await prisma.productInventory.create({
        data: {
          company_id: input.company_id,
          warehouse_id: input.warehouse_id,
          date: dateToIsoString(input.date),
          total,
        },
      })
      const items = await mapAsyncSequential(input.items, item =>
        prisma.productInventoryItem.create({
          data: {
            inventory_id: productInventory.id,
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          },
        }),
      )
      await syncProductInventoryItemTransactions(prisma, items, {
        company_id: input.company_id,
        warehouse_id: input.warehouse_id,
        date: new Date(dateToIsoString(input.date)),
      })
      return {
        ...productInventory,
        date: isoStringToDate(productInventory.date.toISOString()),
        items,
      }
    })
  })
