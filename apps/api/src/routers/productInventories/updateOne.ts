import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
  deleteProductInventoryItemTransactions,
  isoStringToDate,
  mapAsyncSequential,
  syncProductInventoryItemTransactions,
} from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(
    z.object({
      id: z.number(),
      warehouse_id: z.number(),
      date: dateOnlyString,
      items: z.array(
        z.object({
          id: z.number().optional(),
          product_unit_id: z.number(),
          amount: z.number().positive(),
          price: z.number().int().positive(),
        }),
      ),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    const existingInventory = await prisma.productInventory.findUniqueOrThrow({
      where: { id: input.id },
      select: { company_id: true },
    })
    await assertAccess(existingInventory.company_id, ctx.user.id, prisma)
    return await prisma.$transaction(async prisma => {
      const total = input.items.reduce(
        (sum, item) => sum + item.amount * item.price,
        0,
      )
      const existingItems = await prisma.productInventoryItem.findMany({
        where: { inventory_id: input.id },
        select: { id: true },
      })
      const existingItemIds = existingItems.map(item => item.id)
      const inputItemsWithId = input.items.filter(item => item.id)
      const inputItemsWithoutId = input.items.filter(item => !item.id)
      const inputItemIds = inputItemsWithId.map(item => item.id!)
      const itemsToDelete = existingItemIds.filter(
        id => !inputItemIds.includes(id),
      )
      if (itemsToDelete.length > 0) {
        await deleteProductInventoryItemTransactions(prisma, itemsToDelete)
        await prisma.productInventoryItem.deleteMany({
          where: { id: { in: itemsToDelete } },
        })
      }
      const updatedItems = await mapAsyncSequential(inputItemsWithId, item =>
        prisma.productInventoryItem.update({
          where: { id: item.id! },
          data: {
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          },
        }),
      )
      const createdItems = await mapAsyncSequential(inputItemsWithoutId, item =>
        prisma.productInventoryItem.create({
          data: {
            inventory_id: input.id,
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          },
        }),
      )
      const allItems = [...updatedItems, ...createdItems]
      await syncProductInventoryItemTransactions(prisma, allItems, {
        company_id: existingInventory.company_id,
        warehouse_id: input.warehouse_id,
        date: new Date(dateToIsoString(input.date)),
      })
      await prisma.productInventory.update({
        where: { id: input.id },
        data: {
          warehouse_id: input.warehouse_id,
          date: dateToIsoString(input.date),
          total,
        },
      })
      const inventory = await prisma.productInventory.findUniqueOrThrow({
        where: { id: input.id },
        include: {
          warehouse: true,
          items: { include: { product_unit: { include: { product: true } } } },
        },
      })
      return {
        ...inventory,
        date: isoStringToDate(inventory.date.toISOString()),
      }
    })
  })
