import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, isoStringToDate } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number(), company_id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    const inventory = await prisma.productInventory.findUniqueOrThrow({
      where: { id: input.id },
      include: {
        warehouse: true,
        items: { include: { product_unit: { include: { product: true } } } },
      },
    })
    return { ...inventory, date: isoStringToDate(inventory.date.toISOString()) }
  })
