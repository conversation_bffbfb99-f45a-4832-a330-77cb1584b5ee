import { PriceType } from '@prisma/client'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const createOne = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      name: z.string(),
      type: z.enum(PriceType),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.price.create({
      data: {
        company_id: input.company_id,
        name: input.name,
        type: input.type,
      },
    })
  })
