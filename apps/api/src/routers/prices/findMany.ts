import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'

export const findMany = protectedProcedure
  .input(z.object({ company_id: z.number() }))
  .query(async ({ ctx, input }) => {
    return prisma.price.findMany({
      where: {
        company_id: input.company_id,
        company: { permissions: { some: { user_id: ctx.user.id } } },
      },
      orderBy: { name: 'asc' },
    })
  })
