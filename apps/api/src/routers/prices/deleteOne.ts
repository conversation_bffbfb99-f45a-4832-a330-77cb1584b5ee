import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'

export const deleteOne = protectedProcedure
  .input(z.object({ id: z.number() }))
  .mutation(async ({ ctx, input }) => {
    return prisma.price.delete({
      where: {
        id: input.id,
        company: { permissions: { some: { user_id: ctx.user.id } } },
      },
    })
  })
