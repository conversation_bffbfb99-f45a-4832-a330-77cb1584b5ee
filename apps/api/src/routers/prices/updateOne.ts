import { PriceType } from '@prisma/client'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'

export const updateOne = protectedProcedure
  .input(
    z.object({ id: z.number(), name: z.string(), type: z.enum(PriceType) }),
  )
  .mutation(async ({ ctx, input }) => {
    return prisma.price.update({
      where: {
        id: input.id,
        company: { permissions: { some: { user_id: ctx.user.id } } },
      },
      data: { name: input.name, type: input.type },
    })
  })
