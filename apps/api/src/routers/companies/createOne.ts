import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'

export const createOne = protectedProcedure
  .input(z.object({ name: z.string() }))
  .mutation(async ({ ctx, input }) => {
    const company = await prisma.company.create({ data: input })
    await prisma.permission.create({
      data: { company_id: company.id, user_id: ctx.user.id },
    })
    return company
  })
