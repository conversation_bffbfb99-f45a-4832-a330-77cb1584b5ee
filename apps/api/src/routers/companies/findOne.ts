import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.id, ctx.user.id, prisma)
    return prisma.company.findUniqueOrThrow({ where: { id: input.id } })
  })
