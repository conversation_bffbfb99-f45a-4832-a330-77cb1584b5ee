import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(z.object({ id: z.number(), name: z.string() }))
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.id, ctx.user.id, prisma)
    return prisma.company.update({
      where: { id: input.id },
      data: { name: input.name },
    })
  })
