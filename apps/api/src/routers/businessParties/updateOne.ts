import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(
    z.object({
      id: z.number(),
      name: z.string(),
      is_supplier: z.boolean(),
      is_customer: z.boolean(),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    const businessParty = await prisma.businessParty.findUniqueOrThrow({
      where: { id: input.id },
    })
    await assertAccess(businessParty.company_id, ctx.user.id, prisma)
    return prisma.businessParty.update({
      where: { id: input.id },
      data: {
        name: input.name,
        is_supplier: input.is_supplier,
        is_customer: input.is_customer,
      },
    })
  })
