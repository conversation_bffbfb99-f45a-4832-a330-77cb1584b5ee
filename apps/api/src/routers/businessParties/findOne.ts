import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number() }))
  .query(async ({ ctx, input }) => {
    const businessParty = await prisma.businessParty.findUniqueOrThrow({
      where: { id: input.id },
    })
    await assertAccess(businessParty.company_id, ctx.user.id, prisma)
    return businessParty
  })
