import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findMany = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      is_supplier: z.boolean().optional(),
      is_customer: z.boolean().optional(),
    }),
  )
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.businessParty.findMany({
      where: {
        company_id: input.company_id,
        ...(input.is_supplier !== undefined && {
          is_supplier: input.is_supplier,
        }),
        ...(input.is_customer !== undefined && {
          is_customer: input.is_customer,
        }),
      },
    })
  })
