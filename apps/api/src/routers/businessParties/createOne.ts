import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const createOne = protectedProcedure
  .input(
    z.object({
      name: z.string(),
      company_id: z.number(),
      is_supplier: z.boolean(),
      is_customer: z.boolean(),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.businessParty.create({
      data: {
        name: input.name,
        company_id: input.company_id,
        is_supplier: input.is_supplier,
        is_customer: input.is_customer,
      },
    })
  })
