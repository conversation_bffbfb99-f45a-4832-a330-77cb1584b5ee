import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, isoStringToDate } from '../../utils/index.js'

export const findMany = protectedProcedure
  .input(z.object({ company_id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    const priceAdjustments = await prisma.priceAdjustment.findMany({
      where: { company_id: input.company_id },
      include: {
        items: { include: { product_unit: { include: { product: true } } } },
      },
      orderBy: { date: 'desc' },
    })
    return priceAdjustments.map(adjustment => ({
      ...adjustment,
      date: isoStringToDate(adjustment.date.toISOString()),
    }))
  })
