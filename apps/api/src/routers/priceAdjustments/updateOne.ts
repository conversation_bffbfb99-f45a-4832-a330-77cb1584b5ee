import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
  isoStringToDate,
  mapAsyncSequential,
} from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(
    z.object({
      id: z.number(),
      date: dateOnlyString,
      items: z.array(
        z.object({
          id: z.number().optional(),
          product_unit_id: z.number(),
          price: z.number().int().positive(),
        }),
      ),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    const existingAdjustment = await prisma.priceAdjustment.findUniqueOrThrow({
      where: { id: input.id },
      select: { company_id: true },
    })
    await assertAccess(existingAdjustment.company_id, ctx.user.id, prisma)
    return await prisma.$transaction(async prisma => {
      const existingItems = await prisma.priceAdjustmentItem.findMany({
        where: { adjustment_id: input.id },
        select: { id: true },
      })
      const existingItemIds = existingItems.map(item => item.id)
      const inputItemsWithId = input.items.filter(item => item.id)
      const inputItemsWithoutId = input.items.filter(item => !item.id)
      const inputItemIds = inputItemsWithId.map(item => item.id!)
      const itemsToDelete = existingItemIds.filter(
        id => !inputItemIds.includes(id),
      )
      if (itemsToDelete.length > 0) {
        await prisma.priceAdjustmentItem.deleteMany({
          where: { id: { in: itemsToDelete } },
        })
      }
      await mapAsyncSequential(inputItemsWithId, item =>
        prisma.priceAdjustmentItem.update({
          where: { id: item.id! },
          data: { product_unit_id: item.product_unit_id, price: item.price },
        }),
      )
      await mapAsyncSequential(inputItemsWithoutId, item =>
        prisma.priceAdjustmentItem.create({
          data: {
            adjustment_id: input.id,
            product_unit_id: item.product_unit_id,
            price: item.price,
          },
        }),
      )
      await prisma.priceAdjustment.update({
        where: { id: input.id },
        data: { date: dateToIsoString(input.date) },
      })
      const priceAdjustment = await prisma.priceAdjustment.findUniqueOrThrow({
        where: { id: input.id },
        include: {
          items: { include: { product_unit: { include: { product: true } } } },
        },
      })
      return {
        ...priceAdjustment,
        date: isoStringToDate(priceAdjustment.date.toISOString()),
      }
    })
  })
