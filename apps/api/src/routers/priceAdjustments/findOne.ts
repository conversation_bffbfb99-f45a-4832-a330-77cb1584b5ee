import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, isoStringToDate } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number(), company_id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    const priceAdjustment = await prisma.priceAdjustment.findUniqueOrThrow({
      where: { id: input.id },
      include: {
        items: { include: { product_unit: { include: { product: true } } } },
      },
    })
    return {
      ...priceAdjustment,
      date: isoStringToDate(priceAdjustment.date.toISOString()),
    }
  })
