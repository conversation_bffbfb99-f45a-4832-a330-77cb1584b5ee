import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
  mapAsyncSequential,
} from '../../utils/index.js'

export const createOne = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      date: dateOnlyString,
      items: z.array(
        z.object({
          product_unit_id: z.number(),
          price: z.number().int().positive(),
        }),
      ),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return await prisma.$transaction(async prisma => {
      const priceAdjustment = await prisma.priceAdjustment.create({
        data: {
          company_id: input.company_id,
          date: dateToIsoString(input.date),
        },
      })
      const items = await mapAsyncSequential(input.items, item =>
        prisma.priceAdjustmentItem.create({
          data: {
            adjustment_id: priceAdjustment.id,
            product_unit_id: item.product_unit_id,
            price: item.price,
          },
        }),
      )
      return { ...priceAdjustment, items }
    })
  })
