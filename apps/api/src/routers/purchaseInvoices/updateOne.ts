import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
  deletePurchaseInvoiceItemTransactions,
  isoStringToDate,
  mapAsyncSequential,
  syncPurchaseInvoiceItemTransactions,
  syncPurchaseInvoiceMoneyTransaction,
} from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(
    z.object({
      id: z.number(),
      supplier_id: z.number(),
      warehouse_id: z.number(),
      date: dateOnlyString,
      items: z.array(
        z.object({
          id: z.number().optional(),
          product_unit_id: z.number(),
          amount: z.number(),
          price: z.number().int().positive(),
        }),
      ),
    }),
  )
  .mutation(async ({ ctx, input }) => {
    const existingInvoice = await prisma.purchaseInvoice.findUniqueOrThrow({
      where: { id: input.id },
      select: { company_id: true },
    })
    await assertAccess(existingInvoice.company_id, ctx.user.id, prisma)
    return prisma.$transaction(async prisma => {
      const total = input.items.reduce(
        (sum, item) => sum + item.amount * item.price,
        0,
      )
      const existingItems = await prisma.purchaseInvoiceItem.findMany({
        where: { invoice_id: input.id },
        select: { id: true },
      })
      const existingItemIds = existingItems.map(item => item.id)
      const inputItemsWithId = input.items.filter(item => item.id)
      const inputItemsWithoutId = input.items.filter(item => !item.id)
      const inputItemIds = inputItemsWithId.map(item => item.id!)
      const itemsToDelete = existingItemIds.filter(
        id => !inputItemIds.includes(id),
      )
      if (itemsToDelete.length > 0) {
        await deletePurchaseInvoiceItemTransactions(prisma, itemsToDelete)
        await prisma.purchaseInvoiceItem.deleteMany({
          where: { id: { in: itemsToDelete } },
        })
      }
      const updatedItems = await mapAsyncSequential(inputItemsWithId, item =>
        prisma.purchaseInvoiceItem.update({
          where: { id: item.id! },
          data: {
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          },
        }),
      )
      const createdItems = await mapAsyncSequential(inputItemsWithoutId, item =>
        prisma.purchaseInvoiceItem.create({
          data: {
            invoice_id: input.id,
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          },
        }),
      )
      const allItems = [...updatedItems, ...createdItems]
      await syncPurchaseInvoiceItemTransactions(prisma, allItems, {
        company_id: existingInvoice.company_id,
        warehouse_id: input.warehouse_id,
        date: new Date(dateToIsoString(input.date)),
      })
      await prisma.purchaseInvoice.update({
        where: { id: input.id },
        data: {
          supplier_id: input.supplier_id,
          warehouse_id: input.warehouse_id,
          date: dateToIsoString(input.date),
          total,
        },
      })
      await syncPurchaseInvoiceMoneyTransaction(prisma, {
        id: input.id,
        company_id: existingInvoice.company_id,
        supplier_id: input.supplier_id,
        total,
        date: new Date(dateToIsoString(input.date)),
      })
      const invoice = await prisma.purchaseInvoice.findUniqueOrThrow({
        where: { id: input.id },
        include: {
          supplier: true,
          warehouse: true,
          items: { include: { product_unit: { include: { product: true } } } },
        },
      })
      return { ...invoice, date: isoStringToDate(invoice.date.toISOString()) }
    })
  })
