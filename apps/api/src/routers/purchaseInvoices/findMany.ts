import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, isoStringToDate } from '../../utils/index.js'

export const findMany = protectedProcedure
  .input(z.object({ company_id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    const invoices = await prisma.purchaseInvoice.findMany({
      where: { company_id: input.company_id },
      include: {
        supplier: true,
        warehouse: true,
        items: { include: { product_unit: { include: { product: true } } } },
      },
    })
    return invoices.map(invoice => ({
      ...invoice,
      date: isoStringToDate(invoice.date.toISOString()),
    }))
  })
