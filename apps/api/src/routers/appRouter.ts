import { type inferRouterInputs, type inferRouterOutputs } from '@trpc/server'
import { router } from '../trpc/procedures.js'
import { businessPartiesRouter } from './businessParties/index.js'
import { businessPartyInventoriesRouter } from './businessPartyInventories/index.js'
import { companiesRouter } from './companies/index.js'
import { datamatrixRequestsRouter } from './datamatrixRequests/index.js'
import { datamatrixResponsesRouter } from './datamatrixResponses/index.js'
import { paymentAccountsRouter } from './paymentAccounts/index.js'
import { paymentsRouter } from './payments/index.js'
import { priceAdjustmentsRouter } from './priceAdjustments/index.js'
import { pricesRouter } from './prices/index.js'
import { productInventoriesRouter } from './productInventories/index.js'
import { productMovementsRouter } from './productMovements/index.js'
import { productsRouter } from './products/index.js'
import { purchaseInvoicesRouter } from './purchaseInvoices/index.js'
import { salesInvoicesRouter } from './salesInvoices/index.js'
import { tokensRouter } from './tokens/index.js'
import { usersRouter } from './users/index.js'
import { warehousesRouter } from './warehouses/index.js'
import { warehouseTransfersRouter } from './warehouseTransfers/index.js'

export const appRouter = router({
  companies: companiesRouter,
  businessParties: businessPartiesRouter,
  businessPartyInventories: businessPartyInventoriesRouter,
  datamatrixRequests: datamatrixRequestsRouter,
  datamatrixResponses: datamatrixResponsesRouter,
  paymentAccounts: paymentAccountsRouter,
  payments: paymentsRouter,
  priceAdjustments: priceAdjustmentsRouter,
  prices: pricesRouter,
  products: productsRouter,
  productMovements: productMovementsRouter,
  productInventories: productInventoriesRouter,
  purchaseInvoices: purchaseInvoicesRouter,
  salesInvoices: salesInvoicesRouter,
  tokens: tokensRouter,
  users: usersRouter,
  warehouses: warehousesRouter,
  warehouseTransfers: warehouseTransfersRouter,
})

export type AppRouter = typeof appRouter

export type RouterInputs = inferRouterInputs<AppRouter>
export type RouterOutputs = inferRouterOutputs<AppRouter>
