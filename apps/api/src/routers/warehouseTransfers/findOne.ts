import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, isoStringToDate } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number(), company_id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    const transfer = await prisma.warehouseTransfer.findUniqueOrThrow({
      where: { id: input.id, company_id: input.company_id },
      include: {
        from_warehouse: true,
        to_warehouse: true,
        items: { include: { product_unit: { include: { product: true } } } },
      },
    })
    return { ...transfer, date: isoStringToDate(transfer.date.toISOString()) }
  })
