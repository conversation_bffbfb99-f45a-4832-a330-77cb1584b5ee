import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
  deleteWarehouseTransferItemTransactions,
  isoStringToDate,
  mapAsyncSequential,
  syncWarehouseTransferItemTransactions,
  validateWarehouseTransferInventory,
} from '../../utils/index.js'

export const updateOne = protectedProcedure
  .input(
    z
      .object({
        id: z.number(),
        from_warehouse_id: z.number(),
        to_warehouse_id: z.number(),
        date: dateOnlyString,
        items: z.array(
          z.object({
            id: z.number().optional(),
            product_unit_id: z.number(),
            amount: z.number().positive(),
            price: z.number().int().positive(),
          }),
        ),
      })
      .refine(data => data.from_warehouse_id !== data.to_warehouse_id, {
        message: 'Source and destination warehouses must be different',
        path: ['from_warehouse_id', 'to_warehouse_id'],
      }),
  )
  .mutation(async ({ ctx, input }) => {
    const existingTransfer = await prisma.warehouseTransfer.findUniqueOrThrow({
      where: { id: input.id },
      select: { company_id: true },
    })
    await assertAccess(existingTransfer.company_id, ctx.user.id, prisma)
    if (input.from_warehouse_id === input.to_warehouse_id) {
      throw new Error('Source and destination warehouses must be different')
    }
    const [fromWarehouse, toWarehouse] = await Promise.all([
      prisma.warehouse.findUniqueOrThrow({
        where: { id: input.from_warehouse_id },
      }),
      prisma.warehouse.findUniqueOrThrow({
        where: { id: input.to_warehouse_id },
      }),
    ])
    if (fromWarehouse.company_id !== existingTransfer.company_id) {
      throw new Error('Source warehouse not found or access denied')
    }
    if (toWarehouse.company_id !== existingTransfer.company_id) {
      throw new Error('Destination warehouse not found or access denied')
    }
    return await prisma.$transaction(async prisma => {
      await prisma.warehouseTransfer.findUniqueOrThrow({
        where: { id: input.id },
        include: { items: true },
      })
      await validateWarehouseTransferInventory(prisma, input.items, {
        company_id: existingTransfer.company_id,
        from_warehouse_id: input.from_warehouse_id,
      })
      const total = input.items.reduce(
        (sum, item) => sum + item.amount * item.price,
        0,
      )
      const existingItems = await prisma.warehouseTransferItem.findMany({
        where: { transfer_id: input.id },
        select: { id: true },
      })
      const existingItemIds = existingItems.map(item => item.id)
      const inputItemsWithId = input.items.filter(item => item.id)
      const inputItemsWithoutId = input.items.filter(item => !item.id)
      const inputItemIds = inputItemsWithId.map(item => item.id!)
      const itemsToDelete = existingItemIds.filter(
        id => !inputItemIds.includes(id),
      )
      if (itemsToDelete.length > 0) {
        await deleteWarehouseTransferItemTransactions(prisma, itemsToDelete)
        await prisma.warehouseTransferItem.deleteMany({
          where: { id: { in: itemsToDelete } },
        })
      }
      const updatedItems = await mapAsyncSequential(inputItemsWithId, item =>
        prisma.warehouseTransferItem.update({
          where: { id: item.id! },
          data: {
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          },
        }),
      )
      const createdItems = await mapAsyncSequential(inputItemsWithoutId, item =>
        prisma.warehouseTransferItem.create({
          data: {
            transfer_id: input.id,
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          },
        }),
      )
      const allItems = [...updatedItems, ...createdItems]
      await syncWarehouseTransferItemTransactions(prisma, allItems, {
        company_id: existingTransfer.company_id,
        from_warehouse_id: input.from_warehouse_id,
        to_warehouse_id: input.to_warehouse_id,
        date: new Date(dateToIsoString(input.date)),
      })
      await prisma.warehouseTransfer.update({
        where: { id: input.id },
        data: {
          from_warehouse_id: input.from_warehouse_id,
          to_warehouse_id: input.to_warehouse_id,
          date: dateToIsoString(input.date),
          total,
        },
      })
      const transfer = await prisma.warehouseTransfer.findUniqueOrThrow({
        where: { id: input.id },
        include: {
          from_warehouse: true,
          to_warehouse: true,
          items: { include: { product_unit: { include: { product: true } } } },
        },
      })
      return { ...transfer, date: isoStringToDate(transfer.date.toISOString()) }
    })
  })
