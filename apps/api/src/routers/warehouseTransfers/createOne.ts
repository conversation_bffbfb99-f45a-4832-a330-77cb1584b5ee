import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
  isoStringToDate,
  mapAsyncSequential,
  syncWarehouseTransferItemTransactions,
  validateWarehouseTransferInventory,
} from '../../utils/index.js'

export const createOne = protectedProcedure
  .input(
    z
      .object({
        company_id: z.number(),
        from_warehouse_id: z.number(),
        to_warehouse_id: z.number(),
        date: dateOnlyString,
        items: z.array(
          z.object({
            product_unit_id: z.number(),
            amount: z.number().positive(),
            price: z.number().int().positive(),
          }),
        ),
      })
      .refine(data => data.from_warehouse_id !== data.to_warehouse_id, {
        message: 'Source and destination warehouses must be different',
        path: ['from_warehouse_id', 'to_warehouse_id'],
      }),
  )
  .mutation(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)

    if (input.from_warehouse_id === input.to_warehouse_id) {
      throw new Error('Source and destination warehouses must be different')
    }

    const [fromWarehouse, toWarehouse] = await Promise.all([
      prisma.warehouse.findUniqueOrThrow({
        where: { id: input.from_warehouse_id },
      }),
      prisma.warehouse.findUniqueOrThrow({
        where: { id: input.to_warehouse_id },
      }),
    ])

    if (fromWarehouse.company_id !== input.company_id) {
      throw new Error('Source warehouse not found or access denied')
    }

    if (toWarehouse.company_id !== input.company_id) {
      throw new Error('Destination warehouse not found or access denied')
    }

    return await prisma.$transaction(async prisma => {
      await validateWarehouseTransferInventory(prisma, input.items, {
        company_id: input.company_id,
        from_warehouse_id: input.from_warehouse_id,
      })

      const total = input.items.reduce(
        (sum, item) => sum + item.amount * item.price,
        0,
      )

      const warehouseTransfer = await prisma.warehouseTransfer.create({
        data: {
          company_id: input.company_id,
          from_warehouse_id: input.from_warehouse_id,
          to_warehouse_id: input.to_warehouse_id,
          date: dateToIsoString(input.date),
          total,
        },
      })

      const items = await mapAsyncSequential(input.items, item =>
        prisma.warehouseTransferItem.create({
          data: {
            transfer_id: warehouseTransfer.id,
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          },
        }),
      )

      await syncWarehouseTransferItemTransactions(prisma, items, {
        company_id: input.company_id,
        from_warehouse_id: input.from_warehouse_id,
        to_warehouse_id: input.to_warehouse_id,
        date: new Date(dateToIsoString(input.date)),
      })

      return {
        ...warehouseTransfer,
        date: isoStringToDate(warehouseTransfer.date.toISOString()),
        items,
      }
    })
  })
