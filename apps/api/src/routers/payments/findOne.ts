import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess, isoStringToDate } from '../../utils/index.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number(), company_id: z.number() }))
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    const payment = await prisma.payment.findUniqueOrThrow({
      where: { id: input.id, company_id: input.company_id },
      include: {
        business_party: true,
        payment_account: true,
        money_transactions: true,
      },
    })
    return { ...payment, date: isoStringToDate(payment.date.toISOString()) }
  })
