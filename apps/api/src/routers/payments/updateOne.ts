import { PaymentDirection, type Payment } from '@prisma/client'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
} from '../../utils/index.js'
import { syncPaymentMoneyTransaction } from '../../utils/syncMoneyTransactions.js'

export const updateOne = protectedProcedure
  .input(
    z.object({
      id: z.number(),
      business_party_id: z.number().optional(),
      payment_account_id: z.number().optional(),
      amount: z.number().optional(),
      direction: z.enum(PaymentDirection).optional(),
      date: dateOnlyString.optional(),
    }),
  )
  .mutation(async ({ ctx, input }): Promise<Payment> => {
    const existingPayment = await prisma.payment.findUniqueOrThrow({
      where: { id: input.id },
      select: { company_id: true },
    })
    await assertAccess(existingPayment.company_id, ctx.user.id, prisma)
    const { id, ...updateData } = input
    return await prisma.$transaction(async prisma => {
      const payment = await prisma.payment.update({
        where: { id },
        data: {
          business_party_id: updateData.business_party_id,
          payment_account_id: updateData.payment_account_id,
          amount: updateData.amount,
          direction: updateData.direction,
          date: updateData.date ? dateToIsoString(updateData.date) : undefined,
        },
      })
      await syncPaymentMoneyTransaction(prisma, {
        id: payment.id,
        company_id: payment.company_id,
        business_party_id: payment.business_party_id,
        payment_account_id: payment.payment_account_id,
        amount: payment.amount,
        direction: payment.direction,
        date: payment.date,
      })
      return payment
    })
  })
