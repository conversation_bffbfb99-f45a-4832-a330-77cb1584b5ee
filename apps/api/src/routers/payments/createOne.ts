import { PaymentDirection, type Payment } from '@prisma/client'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import {
  assertAccess,
  dateOnlyString,
  dateToIsoString,
} from '../../utils/index.js'
import { syncPaymentMoneyTransaction } from '../../utils/syncMoneyTransactions.js'

export const createOne = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      business_party_id: z.number(),
      payment_account_id: z.number().optional(),
      amount: z.number(),
      direction: z.enum(PaymentDirection),
      date: dateOnlyString,
    }),
  )
  .mutation(async ({ ctx, input }): Promise<Payment> => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return await prisma.$transaction(async prisma => {
      const payment = await prisma.payment.create({
        data: {
          company_id: input.company_id,
          business_party_id: input.business_party_id,
          payment_account_id: input.payment_account_id,
          amount: input.amount,
          direction: input.direction,
          date: dateToIsoString(input.date),
        },
      })
      await syncPaymentMoneyTransaction(prisma, {
        id: payment.id,
        company_id: payment.company_id,
        business_party_id: payment.business_party_id,
        payment_account_id: payment.payment_account_id,
        amount: payment.amount,
        direction: payment.direction,
        date: payment.date,
      })
      return payment
    })
  })
