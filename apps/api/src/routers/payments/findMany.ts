import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'
import { assertAccess } from '../../utils/index.js'

export const findMany = protectedProcedure
  .input(
    z.object({
      company_id: z.number(),
      business_party_id: z.number().optional(),
      payment_account_id: z.number().optional(),
    }),
  )
  .query(async ({ ctx, input }) => {
    await assertAccess(input.company_id, ctx.user.id, prisma)
    return prisma.payment.findMany({
      where: {
        company_id: input.company_id,
        business_party_id: input.business_party_id,
        payment_account_id: input.payment_account_id,
      },
      include: {
        business_party: true,
        payment_account: true,
        money_transactions: true,
      },
      orderBy: { date: 'desc' },
    })
  })
