import * as remeda from 'remeda'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { procedure } from '../../trpc/procedures.js'

export const findOne = procedure
  .input(z.object({ value: z.string() }))
  .query(async ({ input }) => {
    const token = await prisma.token.findUniqueOrThrow({
      where: { value: input.value },
    })
    const user = await prisma.user.findUniqueOrThrow({
      where: { id: token.user_id },
    })
    return { ...token, user: remeda.omit(user, ['password']) }
  })
