import { TRPCError } from '@trpc/server'
import * as remeda from 'remeda'
import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { procedure } from '../../trpc/procedures.js'
import { generateTokenValue } from '../../utils/generateTokenValue.js'
import { hashPassword } from '../../utils/hashPassword.js'

export const createOne = procedure
  .input(z.object({ email: z.string(), password: z.string() }))
  .mutation(async ({ input }) => {
    const user = await prisma.user.findUniqueOrThrow({
      where: { email: input.email },
    })
    const hashedPassword = hashPassword(input.password)
    if (hashedPassword !== user.password) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'Invalid credentials',
      })
    }
    const token = await prisma.token.create({
      data: { user_id: user.id, value: generateTokenValue() },
    })
    return { ...token, user: remeda.omit(user, ['password']) }
  })
