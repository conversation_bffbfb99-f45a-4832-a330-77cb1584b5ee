import { z } from 'zod'
import { prisma } from '../../prisma.js'
import { protectedProcedure } from '../../trpc/procedures.js'

export const findOne = protectedProcedure
  .input(z.object({ id: z.number() }))
  .query(({ input, ctx }) => {
    return prisma.warehouse.findFirst({
      where: {
        id: input.id,
        company: { permissions: { some: { user_id: ctx.user.id } } },
      },
    })
  })
