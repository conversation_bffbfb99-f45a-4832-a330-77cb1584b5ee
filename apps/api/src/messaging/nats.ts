import { connect, type NatsConnection } from 'nats'

let connectionPromise: Promise<NatsConnection> | null = null

export async function getConnection() {
  if (!connectionPromise) {
    connectionPromise = connect({ servers: 'localhost:4222' })
  }
  return connectionPromise
}

export async function publishStockDelta(delta: {
  warehouse_id: number
  product_unit_id: number
  quantity: number
  date: string
}) {
  const connection = await getConnection()
  connection.publish('stock.delta', JSON.stringify(delta))
}
