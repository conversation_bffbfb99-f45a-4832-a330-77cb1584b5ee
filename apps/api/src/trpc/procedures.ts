import { initTRPC, TRPCError } from '@trpc/server'
import { parseAuth } from '../utils/parseAuth.js'
import { type Context } from './context.js'

export const { middleware, router, procedure } = initTRPC
  .context<Context>()
  .create()

const authMiddleware = middleware(async ({ ctx, next }) => {
  try {
    const { user } = await parseAuth(ctx.req)
    return await next({ ctx: { ...ctx, user } })
  } catch (_error) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Authentication required',
    })
  }
})

export const protectedProcedure = procedure.use(authMiddleware)
