import assert from 'node:assert'
import type http from 'node:http'
import { prisma } from '../prisma.js'

const parseAccessTokenFromHeader = (authorization: string) => {
  const result = /^Bearer (.+)$/.exec(authorization)
  return result === null ? null : result[1]
}

export const parseAuth = async (request: http.IncomingMessage) => {
  const { authorization } = request.headers
  assert.ok(authorization, new Error('UNAUTHORIZED'))
  const tokenValue = parseAccessTokenFromHeader(authorization)
  assert.ok(tokenValue, new Error('UNAUTHORIZED'))
  return prisma.token.findUniqueOrThrow({
    include: { user: true },
    where: { value: tokenValue },
  })
}
