import type { Prisma } from '@prisma/client'
import { updateProductStock } from './stockManager.js'

export async function syncPurchaseInvoiceItemTransactions(
  prisma: Prisma.TransactionClient,
  items: Array<{
    id: number
    product_unit_id: number
    amount: number
    price: number
  }>,
  invoiceData: { company_id: number; warehouse_id: number; date: Date },
) {
  const existingTransactions = await prisma.productTransaction.findMany({
    where: { purchase_invoice_item_id: { in: items.map(item => item.id) } },
  })

  const existingTransactionsByItemId = new Map(
    existingTransactions.map(t => [t.purchase_invoice_item_id!, t]),
  )

  for (const item of items) {
    const existingTransaction = existingTransactionsByItemId.get(item.id)

    if (existingTransaction) {
      const quantityDelta = item.amount - existingTransaction.quantity

      await prisma.productTransaction.update({
        where: { id: existingTransaction.id },
        data: {
          quantity: item.amount,
          price: item.price,
          date: invoiceData.date,
        },
      })

      if (quantityDelta !== 0) {
        await updateProductStock(prisma, {
          company_id: invoiceData.company_id,
          warehouse_id: invoiceData.warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: quantityDelta,
          date: invoiceData.date,
        })
      }
    } else {
      await prisma.productTransaction.create({
        data: {
          company_id: invoiceData.company_id,
          warehouse_id: invoiceData.warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: item.amount,
          price: item.price,
          date: invoiceData.date,
          purchase_invoice_item_id: item.id,
        },
      })

      await updateProductStock(prisma, {
        company_id: invoiceData.company_id,
        warehouse_id: invoiceData.warehouse_id,
        product_unit_id: item.product_unit_id,
        quantity: item.amount,
        date: invoiceData.date,
      })
    }
  }
}

export async function deletePurchaseInvoiceItemTransactions(
  prisma: Prisma.TransactionClient,
  itemIds: Array<number>,
) {
  const transactions = await prisma.productTransaction.findMany({
    where: { purchase_invoice_item_id: { in: itemIds } },
    select: {
      company_id: true,
      warehouse_id: true,
      product_unit_id: true,
      quantity: true,
      date: true,
    },
  })

  await prisma.productTransaction.deleteMany({
    where: { purchase_invoice_item_id: { in: itemIds } },
  })

  for (const transaction of transactions) {
    await updateProductStock(prisma, {
      company_id: transaction.company_id,
      warehouse_id: transaction.warehouse_id,
      product_unit_id: transaction.product_unit_id,
      quantity: -transaction.quantity,
      date: transaction.date,
    })
  }
}

export async function syncSalesInvoiceItemTransactions(
  prisma: Prisma.TransactionClient,
  items: Array<{
    id: number
    product_unit_id: number
    amount: number
    price: number
  }>,
  invoiceData: { company_id: number; warehouse_id: number; date: Date },
) {
  const existingTransactions = await prisma.productTransaction.findMany({
    where: { sales_invoice_item_id: { in: items.map(item => item.id) } },
  })

  const existingTransactionsByItemId = new Map(
    existingTransactions.map(t => [t.sales_invoice_item_id!, t]),
  )

  for (const item of items) {
    const existingTransaction = existingTransactionsByItemId.get(item.id)

    if (existingTransaction) {
      const quantityDelta = -item.amount - existingTransaction.quantity

      await prisma.productTransaction.update({
        where: { id: existingTransaction.id },
        data: {
          quantity: -item.amount,
          price: item.price,
          date: invoiceData.date,
        },
      })

      if (quantityDelta !== 0) {
        await updateProductStock(prisma, {
          company_id: invoiceData.company_id,
          warehouse_id: invoiceData.warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: quantityDelta,
          date: invoiceData.date,
        })
      }
    } else {
      await prisma.productTransaction.create({
        data: {
          company_id: invoiceData.company_id,
          warehouse_id: invoiceData.warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: -item.amount,
          price: item.price,
          date: invoiceData.date,
          sales_invoice_item_id: item.id,
        },
      })

      await updateProductStock(prisma, {
        company_id: invoiceData.company_id,
        warehouse_id: invoiceData.warehouse_id,
        product_unit_id: item.product_unit_id,
        quantity: -item.amount,
        date: invoiceData.date,
      })
    }
  }
}

export async function deleteSalesInvoiceItemTransactions(
  prisma: Prisma.TransactionClient,
  itemIds: Array<number>,
) {
  const transactions = await prisma.productTransaction.findMany({
    where: { sales_invoice_item_id: { in: itemIds } },
    select: {
      company_id: true,
      warehouse_id: true,
      product_unit_id: true,
      quantity: true,
      date: true,
    },
  })

  await prisma.productTransaction.deleteMany({
    where: { sales_invoice_item_id: { in: itemIds } },
  })

  for (const transaction of transactions) {
    await updateProductStock(prisma, {
      company_id: transaction.company_id,
      warehouse_id: transaction.warehouse_id,
      product_unit_id: transaction.product_unit_id,
      quantity: -transaction.quantity,
      date: transaction.date,
    })
  }
}

export async function syncProductInventoryItemTransactions(
  prisma: Prisma.TransactionClient,
  items: Array<{
    id: number
    product_unit_id: number
    amount: number
    price: number
  }>,
  inventoryData: { company_id: number; warehouse_id: number; date: Date },
) {
  const existingTransactions = await prisma.productTransaction.findMany({
    where: { product_inventory_item_id: { in: items.map(item => item.id) } },
  })

  const existingTransactionsByItemId = new Map(
    existingTransactions.map(t => [t.product_inventory_item_id!, t]),
  )

  for (const item of items) {
    const existingTransaction = existingTransactionsByItemId.get(item.id)

    if (existingTransaction) {
      const quantityDelta = item.amount - existingTransaction.quantity

      await prisma.productTransaction.update({
        where: { id: existingTransaction.id },
        data: {
          quantity: item.amount,
          price: item.price,
          date: inventoryData.date,
        },
      })

      if (quantityDelta !== 0) {
        await updateProductStock(prisma, {
          company_id: inventoryData.company_id,
          warehouse_id: inventoryData.warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: quantityDelta,
          date: inventoryData.date,
        })
      }
    } else {
      await prisma.productTransaction.create({
        data: {
          company_id: inventoryData.company_id,
          warehouse_id: inventoryData.warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: item.amount,
          price: item.price,
          date: inventoryData.date,
          product_inventory_item_id: item.id,
        },
      })

      await updateProductStock(prisma, {
        company_id: inventoryData.company_id,
        warehouse_id: inventoryData.warehouse_id,
        product_unit_id: item.product_unit_id,
        quantity: item.amount,
        date: inventoryData.date,
      })
    }
  }
}

export async function deleteProductInventoryItemTransactions(
  prisma: Prisma.TransactionClient,
  itemIds: Array<number>,
) {
  const transactions = await prisma.productTransaction.findMany({
    where: { product_inventory_item_id: { in: itemIds } },
    select: {
      company_id: true,
      warehouse_id: true,
      product_unit_id: true,
      quantity: true,
      date: true,
    },
  })

  await prisma.productTransaction.deleteMany({
    where: { product_inventory_item_id: { in: itemIds } },
  })

  for (const transaction of transactions) {
    await updateProductStock(prisma, {
      company_id: transaction.company_id,
      warehouse_id: transaction.warehouse_id,
      product_unit_id: transaction.product_unit_id,
      quantity: -transaction.quantity,
      date: transaction.date,
    })
  }
}

export async function syncWarehouseTransferItemTransactions(
  prisma: Prisma.TransactionClient,
  items: Array<{
    id: number
    product_unit_id: number
    amount: number
    price: number
  }>,
  transferData: {
    company_id: number
    from_warehouse_id: number
    to_warehouse_id: number
    date: Date
  },
) {
  const existingTransactions = await prisma.productTransaction.findMany({
    where: { warehouse_transfer_item_id: { in: items.map(item => item.id) } },
  })

  const existingTransactionsByItemId = new Map<
    number,
    Array<{ id: number; warehouse_id: number; quantity: number }>
  >()
  for (const t of existingTransactions) {
    const itemId = t.warehouse_transfer_item_id!
    if (!existingTransactionsByItemId.has(itemId)) {
      existingTransactionsByItemId.set(itemId, [])
    }
    existingTransactionsByItemId.get(itemId)!.push(t)
  }

  for (const item of items) {
    const itemTransactions = existingTransactionsByItemId.get(item.id) || []
    const existingOutTransaction = itemTransactions.find(
      t => t.warehouse_id === transferData.from_warehouse_id,
    )
    const existingInTransaction = itemTransactions.find(
      t => t.warehouse_id === transferData.to_warehouse_id,
    )

    if (existingOutTransaction) {
      const quantityDelta = -item.amount - existingOutTransaction.quantity

      await prisma.productTransaction.update({
        where: { id: existingOutTransaction.id },
        data: {
          quantity: -item.amount,
          price: item.price,
          date: transferData.date,
        },
      })

      if (quantityDelta !== 0) {
        await updateProductStock(prisma, {
          company_id: transferData.company_id,
          warehouse_id: transferData.from_warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: quantityDelta,
          date: transferData.date,
        })
      }
    } else {
      await prisma.productTransaction.create({
        data: {
          company_id: transferData.company_id,
          warehouse_id: transferData.from_warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: -item.amount,
          price: item.price,
          date: transferData.date,
          warehouse_transfer_item_id: item.id,
        },
      })

      await updateProductStock(prisma, {
        company_id: transferData.company_id,
        warehouse_id: transferData.from_warehouse_id,
        product_unit_id: item.product_unit_id,
        quantity: -item.amount,
        date: transferData.date,
      })
    }

    if (existingInTransaction) {
      const quantityDelta = item.amount - existingInTransaction.quantity

      await prisma.productTransaction.update({
        where: { id: existingInTransaction.id },
        data: {
          quantity: item.amount,
          price: item.price,
          date: transferData.date,
        },
      })

      if (quantityDelta !== 0) {
        await updateProductStock(prisma, {
          company_id: transferData.company_id,
          warehouse_id: transferData.to_warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: quantityDelta,
          date: transferData.date,
        })
      }
    } else {
      await prisma.productTransaction.create({
        data: {
          company_id: transferData.company_id,
          warehouse_id: transferData.to_warehouse_id,
          product_unit_id: item.product_unit_id,
          quantity: item.amount,
          price: item.price,
          date: transferData.date,
          warehouse_transfer_item_id: item.id,
        },
      })

      await updateProductStock(prisma, {
        company_id: transferData.company_id,
        warehouse_id: transferData.to_warehouse_id,
        product_unit_id: item.product_unit_id,
        quantity: item.amount,
        date: transferData.date,
      })
    }
  }
}

export async function deleteWarehouseTransferItemTransactions(
  prisma: Prisma.TransactionClient,
  itemIds: Array<number>,
) {
  await prisma.productTransaction.deleteMany({
    where: { warehouse_transfer_item_id: { in: itemIds } },
  })
}

export async function validateWarehouseTransferInventory(
  prisma: Prisma.TransactionClient,
  items: Array<{ product_unit_id: number; amount: number }>,
  transferData: { company_id: number; from_warehouse_id: number },
) {
  for (const item of items) {
    const totalTransactions = await prisma.productTransaction.aggregate({
      where: {
        company_id: transferData.company_id,
        warehouse_id: transferData.from_warehouse_id,
        product_unit_id: item.product_unit_id,
      },
      _sum: { quantity: true },
    })

    const currentStock = totalTransactions._sum.quantity || 0

    if (currentStock < item.amount) {
      throw new Error(
        `Insufficient inventory for product unit ID ${item.product_unit_id}. Available: ${currentStock}, Required: ${item.amount}`,
      )
    }
  }
}
