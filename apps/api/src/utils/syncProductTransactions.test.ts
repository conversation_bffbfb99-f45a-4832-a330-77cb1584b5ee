import { PrismaClient } from '@prisma/client'
import { beforeEach, describe, expect, it } from 'vitest'
import {
  deletePurchaseInvoiceItemTransactions,
  syncProductInventoryItemTransactions,
  syncPurchaseInvoiceItemTransactions,
  syncSalesInvoiceItemTransactions,
} from './syncProductTransactions.js'

const prisma = new PrismaClient()

describe('Product Transaction Syncing', () => {
  let companyId: number
  let warehouseId: number
  let productId: number
  let productUnitId: number
  let businessPartyId: number

  beforeEach(async () => {
    const company = await prisma.company.create({
      data: { name: 'Test Company' },
    })
    companyId = company.id

    const warehouse = await prisma.warehouse.create({
      data: { company_id: companyId, name: 'Test Warehouse' },
    })
    warehouseId = warehouse.id

    const product = await prisma.product.create({
      data: { company_id: companyId, name: 'Test Product' },
    })
    productId = product.id

    const productUnit = await prisma.productUnit.create({
      data: {
        company_id: companyId,
        product_id: productId,
        name: 'piece',
        multiplier: 1,
        barcode: `TEST${Date.now()}`,
      },
    })
    productUnitId = productUnit.id

    const businessParty = await prisma.businessParty.create({
      data: {
        company_id: companyId,
        name: 'Test Business Party',
        is_supplier: true,
        is_customer: true,
      },
    })
    businessPartyId = businessParty.id
  })

  it('should sync purchase invoice item transactions', async () => {
    await prisma.$transaction(async prisma => {
      const purchaseInvoice = await prisma.purchaseInvoice.create({
        data: {
          company_id: companyId,
          supplier_id: businessPartyId,
          warehouse_id: warehouseId,
          date: new Date(),
          total: 1000,
        },
      })

      const item = await prisma.purchaseInvoiceItem.create({
        data: {
          invoice_id: purchaseInvoice.id,
          product_unit_id: productUnitId,
          amount: 10,
          price: 100,
        },
      })

      await syncPurchaseInvoiceItemTransactions(prisma, [item], {
        company_id: companyId,
        warehouse_id: warehouseId,
        date: new Date(),
      })

      const transactions = await prisma.productTransaction.findMany({
        where: { purchase_invoice_item_id: item.id },
      })

      expect(transactions).toHaveLength(1)
      expect(transactions[0].quantity).toBe(10)
      expect(transactions[0].price).toBe(100)
      expect(transactions[0].product_unit_id).toBe(productUnitId)
    })
  })

  it('should sync sales invoice item transactions with negative quantities', async () => {
    await prisma.$transaction(async prisma => {
      const salesInvoice = await prisma.salesInvoice.create({
        data: {
          company_id: companyId,
          customer_id: businessPartyId,
          warehouse_id: warehouseId,
          date: new Date(),
          total: 500,
        },
      })

      const item = await prisma.salesInvoiceItem.create({
        data: {
          invoice_id: salesInvoice.id,
          product_unit_id: productUnitId,
          amount: 5,
          price: 100,
        },
      })

      await syncSalesInvoiceItemTransactions(prisma, [item], {
        company_id: companyId,
        warehouse_id: warehouseId,
        date: new Date(),
      })

      const transactions = await prisma.productTransaction.findMany({
        where: { sales_invoice_item_id: item.id },
      })

      expect(transactions).toHaveLength(1)
      expect(transactions[0].quantity).toBe(-5)
      expect(transactions[0].price).toBe(100)
      expect(transactions[0].product_unit_id).toBe(productUnitId)
    })
  })

  it('should sync product inventory item transactions', async () => {
    await prisma.$transaction(async prisma => {
      const productInventory = await prisma.productInventory.create({
        data: {
          company_id: companyId,
          warehouse_id: warehouseId,
          date: new Date(),
          total: 2000,
        },
      })

      const item = await prisma.productInventoryItem.create({
        data: {
          inventory_id: productInventory.id,
          product_unit_id: productUnitId,
          amount: 20,
          price: 100,
        },
      })

      await syncProductInventoryItemTransactions(prisma, [item], {
        company_id: companyId,
        warehouse_id: warehouseId,
        date: new Date(),
      })

      const transactions = await prisma.productTransaction.findMany({
        where: { product_inventory_item_id: item.id },
      })

      expect(transactions).toHaveLength(1)
      expect(transactions[0].quantity).toBe(20)
      expect(transactions[0].price).toBe(100)
      expect(transactions[0].product_unit_id).toBe(productUnitId)
    })
  })

  it('should delete purchase invoice item transactions', async () => {
    await prisma.$transaction(async prisma => {
      const purchaseInvoice = await prisma.purchaseInvoice.create({
        data: {
          company_id: companyId,
          supplier_id: businessPartyId,
          warehouse_id: warehouseId,
          date: new Date(),
          total: 1000,
        },
      })

      const item = await prisma.purchaseInvoiceItem.create({
        data: {
          invoice_id: purchaseInvoice.id,
          product_unit_id: productUnitId,
          amount: 10,
          price: 100,
        },
      })

      await prisma.productTransaction.create({
        data: {
          company_id: companyId,
          warehouse_id: warehouseId,
          product_unit_id: productUnitId,
          quantity: 10,
          price: 100,
          purchase_invoice_item_id: item.id,
          date: new Date(),
        },
      })

      await deletePurchaseInvoiceItemTransactions(prisma, [item.id])

      const transactions = await prisma.productTransaction.findMany({
        where: { purchase_invoice_item_id: item.id },
      })

      expect(transactions).toHaveLength(0)
    })
  })
})
