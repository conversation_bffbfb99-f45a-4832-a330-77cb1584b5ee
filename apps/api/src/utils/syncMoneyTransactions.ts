import type { Prisma } from '@prisma/client'

export async function syncPurchaseInvoiceMoneyTransaction(
  prisma: Prisma.TransactionClient,
  invoice: {
    id: number
    company_id: number
    supplier_id: number
    total: number
    date: Date
  },
) {
  const existing = await prisma.moneyTransaction.findFirst({
    where: { purchase_invoice_id: invoice.id },
  })

  if (existing) {
    await prisma.moneyTransaction.update({
      where: { id: existing.id },
      data: {
        company_id: invoice.company_id,
        business_party_id: invoice.supplier_id,
        amount: -invoice.total,
        date: invoice.date,
      },
    })
  } else {
    await prisma.moneyTransaction.create({
      data: {
        company_id: invoice.company_id,
        business_party_id: invoice.supplier_id,
        amount: -invoice.total,
        date: invoice.date,
        purchase_invoice_id: invoice.id,
      },
    })
  }
}

export async function syncPaymentMoneyTransaction(
  prisma: Prisma.TransactionClient,
  payment: {
    id: number
    company_id: number
    business_party_id: number
    payment_account_id: number | null
    amount: number
    direction: 'incoming' | 'outgoing'
    date: Date
  },
) {
  const existing = await prisma.moneyTransaction.findFirst({
    where: { payment_id: payment.id },
  })

  const transactionAmount =
    payment.direction === 'incoming' ? payment.amount : -payment.amount

  if (existing) {
    await prisma.moneyTransaction.update({
      where: { id: existing.id },
      data: {
        company_id: payment.company_id,
        business_party_id: payment.business_party_id,
        payment_account_id: payment.payment_account_id,
        amount: transactionAmount,
        date: payment.date,
      },
    })
  } else {
    await prisma.moneyTransaction.create({
      data: {
        company_id: payment.company_id,
        business_party_id: payment.business_party_id,
        payment_account_id: payment.payment_account_id,
        amount: transactionAmount,
        date: payment.date,
        payment_id: payment.id,
      },
    })
  }
}

export async function syncSalesInvoiceMoneyTransaction(
  prisma: Prisma.TransactionClient,
  invoice: {
    id: number
    company_id: number
    customer_id: number
    total: number
    date: Date
  },
) {
  const existing = await prisma.moneyTransaction.findFirst({
    where: { sales_invoice_id: invoice.id },
  })

  if (existing) {
    await prisma.moneyTransaction.update({
      where: { id: existing.id },
      data: {
        company_id: invoice.company_id,
        business_party_id: invoice.customer_id,
        amount: invoice.total,
        date: invoice.date,
      },
    })
  } else {
    await prisma.moneyTransaction.create({
      data: {
        company_id: invoice.company_id,
        business_party_id: invoice.customer_id,
        amount: invoice.total,
        date: invoice.date,
        sales_invoice_id: invoice.id,
      },
    })
  }
}
