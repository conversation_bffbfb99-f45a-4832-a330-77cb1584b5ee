import { describe, expect, it } from 'vitest'
import { dateOnlyString } from './schema.js'

describe('dateOnlyString', () => {
  it.each(['2024-02-29', '2025-01-01', '1999-12-31'])(
    'accepts valid yyyy-MM-dd date: %s',
    input => {
      expect(dateOnlyString.parse(input)).toBe(input)
    },
  )

  it.each(['24-02-29', '2024/02/29', 'invalid-date'])(
    'rejects invalid date format: %s',
    date => {
      const result = dateOnlyString.safeParse(date)
      expect(result.success).toBe(false)
    },
  )

  it.each(['9999-12-31', '1000-06-15', '8000-03-20'])(
    'handles extreme date values: %s',
    date => {
      expect(dateOnlyString.parse(date)).toBe(date)
    },
  )

  it.each(['2021', '2023', '2025'])(
    'validates february dates in non-leap year: %s',
    year => {
      expect(dateOnlyString.parse(`${year}-02-28`)).toBe(`${year}-02-28`)

      const result = dateOnlyString.safeParse(`${year}-02-29`)
      expect(result.success).toBe(false)
    },
  )

  it.each(['01', '07', '12'])('validates 31-day months: %s', month => {
    expect(dateOnlyString.parse(`2024-${month}-31`)).toBe(`2024-${month}-31`)
  })

  it.each(['04', '06', '11'])('validates 30-day months: %s', month => {
    expect(dateOnlyString.parse(`2024-${month}-30`)).toBe(`2024-${month}-30`)

    const result = dateOnlyString.safeParse(`2024-${month}-31`)
    expect(result.success).toBe(false)
  })

  it.each(['13', '00', '99'])('rejects invalid month value: %s', month => {
    const result = dateOnlyString.safeParse(`2024-${month}-15`)
    expect(result.success).toBe(false)
  })

  it.each(['00', '32', '99'])('rejects invalid day value: %s', day => {
    const result = dateOnlyString.safeParse(`2024-06-${day}`)
    expect(result.success).toBe(false)
  })

  it.each(['2024-02-29', '2024-03-01', '2024-04-30'])(
    'handles edge cases for month transitions: %s',
    date => {
      expect(dateOnlyString.parse(date)).toBe(date)
    },
  )

  it.each(['2024-01-01', '2024-06-15', '2024-12-31'])(
    'does not change date due to timezone: %s',
    dateString => {
      const parsed = dateOnlyString.parse(dateString)
      expect(parsed).toBe(dateString)

      const date = new Date(dateString + 'T00:00:00.000Z')
      expect(date.toISOString().split('T')[0]).toBe(dateString)
    },
  )

  it.each(['2024-02-29', '2000-02-29', '1600-02-29'])(
    'correctly validates leap years: %s',
    date => {
      expect(dateOnlyString.parse(date)).toBe(date)
    },
  )

  it.each(['2023-02-29', '1900-02-29', '2100-02-29'])(
    'rejects invalid leap year date: %s',
    date => {
      const result = dateOnlyString.safeParse(date)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0]?.message).toBe('Invalid calendar date')
      }
    },
  )
})
