import { PrismaClient } from '@prisma/client'
import { beforeEach, describe, expect, it } from 'vitest'
import {
  syncPurchaseInvoiceMoneyTransaction,
  syncSalesInvoiceMoneyTransaction,
} from './syncMoneyTransactions.js'

const prisma = new PrismaClient()

describe('Money Transaction Syncing', () => {
  let companyId: number
  let warehouseId: number
  let supplierId: number
  let customerId: number

  beforeEach(async () => {
    const company = await prisma.company.create({ data: { name: 'Test Co' } })
    companyId = company.id

    const warehouse = await prisma.warehouse.create({
      data: { company_id: companyId, name: 'W1' },
    })
    warehouseId = warehouse.id

    const supplier = await prisma.businessParty.create({
      data: {
        company_id: companyId,
        name: 'Supplier',
        is_supplier: true,
        is_customer: false,
      },
    })
    supplierId = supplier.id

    const customer = await prisma.businessParty.create({
      data: {
        company_id: companyId,
        name: 'Customer',
        is_supplier: false,
        is_customer: true,
      },
    })
    customerId = customer.id
  })

  it('creates purchase invoice money transaction with negative amount', async () => {
    await prisma.$transaction(async prisma => {
      const purchaseInvoice = await prisma.purchaseInvoice.create({
        data: {
          company_id: companyId,
          supplier_id: supplierId,
          warehouse_id: warehouseId,
          date: new Date(),
          total: 1234,
        },
      })

      await syncPurchaseInvoiceMoneyTransaction(prisma, {
        id: purchaseInvoice.id,
        company_id: companyId,
        supplier_id: supplierId,
        total: 1234,
        date: new Date(),
      })

      const transactions = await prisma.moneyTransaction.findMany({
        where: { purchase_invoice_id: purchaseInvoice.id },
      })

      expect(transactions).toHaveLength(1)
      expect(transactions[0].amount).toBe(-1234)
      expect(transactions[0].business_party_id).toBe(supplierId)
    })
  })

  it('creates sales invoice money transaction with positive amount', async () => {
    await prisma.$transaction(async prisma => {
      const salesInvoice = await prisma.salesInvoice.create({
        data: {
          company_id: companyId,
          customer_id: customerId,
          warehouse_id: warehouseId,
          date: new Date(),
          total: 567,
        },
      })

      await syncSalesInvoiceMoneyTransaction(prisma, {
        id: salesInvoice.id,
        company_id: companyId,
        customer_id: customerId,
        total: 567,
        date: new Date(),
      })

      const transactions = await prisma.moneyTransaction.findMany({
        where: { sales_invoice_id: salesInvoice.id },
      })

      expect(transactions).toHaveLength(1)
      expect(transactions[0].amount).toBe(567)
      expect(transactions[0].business_party_id).toBe(customerId)
    })
  })
})
