import { z } from 'zod'

export const dateOnlyString = z.string().superRefine((arg, ctx) => {
  if (!/^\d{4}-\d{2}-\d{2}$/.test(arg))
    return ctx.addIssue({
      code: 'custom',
      message: 'Date must be in yyyy-MM-dd format',
    })
  const [year, month, day] = arg.split('-').map(Number)
  const date = new Date(Date.UTC(year, month - 1, day, 12))
  if (date.toISOString().slice(0, 10) !== arg)
    ctx.addIssue({ code: 'custom', message: 'Invalid calendar date' })
})
