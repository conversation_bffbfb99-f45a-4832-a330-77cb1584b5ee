import type { Prisma } from '@prisma/client'
import { publishStockDelta } from '../messaging/nats.js'

export type StockDelta = {
  company_id: number
  warehouse_id: number
  product_unit_id: number
  quantity: number
  date: Date
}

export const updateProductStock = async (
  prisma: Prisma.TransactionClient,
  delta: StockDelta,
) => {
  const maxRetries = 3
  let attempt = 0
  while (attempt < maxRetries) {
    try {
      await prisma.$executeRaw`
        INSERT INTO product_stocks (company_id, warehouse_id, product_unit_id, quantity)
        VALUES (${delta.company_id}, ${delta.warehouse_id}, ${delta.product_unit_id}, ${delta.quantity})
        ON CONFLICT (company_id, warehouse_id, product_unit_id)
        DO UPDATE SET quantity = product_stocks.quantity + ${delta.quantity}
      `
      await publishStockDelta({
        warehouse_id: delta.warehouse_id,
        product_unit_id: delta.product_unit_id,
        quantity: delta.quantity,
        date: delta.date.toISOString(),
      })
      return
    } catch (error) {
      attempt++
      if (attempt >= maxRetries) {
        throw new Error(
          `Failed to update product stock after ${maxRetries} attempts: ${error}`,
        )
      }
      await new Promise(resolve => setTimeout(resolve, 10 * attempt))
    }
  }
}

export const validateStockAvailability = async (
  prisma: Prisma.TransactionClient,
  items: Array<{
    company_id: number
    warehouse_id: number
    product_unit_id: number
    required_quantity: number
  }>,
) => {
  for (const item of items) {
    const stock = await prisma.productStock.findUnique({
      where: {
        company_id_warehouse_id_product_unit_id: {
          company_id: item.company_id,
          warehouse_id: item.warehouse_id,
          product_unit_id: item.product_unit_id,
        },
      },
      select: { quantity: true },
    })
    const currentStock = stock?.quantity || 0
    if (currentStock < item.required_quantity) {
      const productUnit = await prisma.productUnit.findUnique({
        where: { id: item.product_unit_id },
        include: { product: true },
      })
      throw new Error(
        `Insufficient stock for ${productUnit?.product.name} (${productUnit?.name}). Available: ${currentStock}, Required: ${item.required_quantity}`,
      )
    }
  }
}

export const getProductStock = async (
  prisma: Prisma.TransactionClient,
  company_id: number,
  warehouse_id: number,
  product_unit_id: number,
) => {
  const stock = await prisma.productStock.findUnique({
    where: {
      company_id_warehouse_id_product_unit_id: {
        company_id,
        warehouse_id,
        product_unit_id,
      },
    },
    select: { quantity: true },
  })
  return stock?.quantity || 0
}

export const getWarehouseStocks = async (
  prisma: Prisma.TransactionClient,
  company_id: number,
  warehouse_id: number,
) => {
  const stocks = await prisma.productStock.findMany({
    where: { company_id, warehouse_id, quantity: { gt: 0 } },
    include: { product_unit: { include: { product: true } } },
    orderBy: [
      { product_unit: { product: { name: 'asc' } } },
      { product_unit: { name: 'asc' } },
    ],
  })
  return stocks.map(stock => ({
    product_unit_id: stock.product_unit_id,
    product_name: stock.product_unit.product.name,
    unit_name: stock.product_unit.name,
    quantity: stock.quantity,
  }))
}

export const reconcileProductStock = async (
  prisma: Prisma.TransactionClient,
  company_id: number,
  warehouse_id: number,
  product_unit_id: number,
) => {
  const stockRecord = await prisma.productStock.findUnique({
    where: {
      company_id_warehouse_id_product_unit_id: {
        company_id,
        warehouse_id,
        product_unit_id,
      },
    },
  })
  const transactionSum = await prisma.productTransaction.aggregate({
    where: { company_id, warehouse_id, product_unit_id },
    _sum: { quantity: true },
  })
  const stockTableQuantity = stockRecord?.quantity || 0
  const transactionSumQuantity = transactionSum._sum.quantity || 0
  const difference = stockTableQuantity - transactionSumQuantity
  let fixed = false
  if (difference !== 0) {
    if (stockRecord) {
      await prisma.productStock.update({
        where: { id: stockRecord.id },
        data: { quantity: transactionSumQuantity },
      })
    } else if (transactionSumQuantity !== 0) {
      await prisma.productStock.create({
        data: {
          company_id,
          warehouse_id,
          product_unit_id,
          quantity: transactionSumQuantity,
        },
      })
    }
    fixed = true
  }
  return {
    stock_table_quantity: stockTableQuantity,
    transaction_sum_quantity: transactionSumQuantity,
    difference,
    fixed,
  }
}
