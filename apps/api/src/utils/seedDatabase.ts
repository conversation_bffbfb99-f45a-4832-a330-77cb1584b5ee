import { PaymentAccountType, PriceType } from '@prisma/client'
import { ADMIN_EMAIL, ADMIN_PASSWORD, NODE_ENV } from '../constants.js'
import { prisma } from '../prisma.js'
import { findOrCreate, hashPassword } from './index.js'

const mockCompanies = [{ name: 'Company 1' }, { name: 'Company 2' }]

const mockBusinessParties = [
  { name: 'Supplier 1', is_supplier: true, is_customer: false },
  { name: 'Supplier 2', is_supplier: true, is_customer: false },
  { name: 'Customer 1', is_supplier: false, is_customer: true },
  { name: 'Customer 2', is_supplier: false, is_customer: true },
]

const mockWarehouses = [{ name: 'Warehouse 1' }, { name: 'Warehouse 2' }]

const mockPaymentAccounts = [
  { name: 'Bank Account 1', type: PaymentAccountType.bank },
  { name: 'Cash Account 1', type: PaymentAccountType.cash },
]

const mockPrices = [
  { name: 'Retail', type: PriceType.purchase },
  { name: 'Wholesale', type: PriceType.purchase },
  { name: 'Retail', type: PriceType.sales },
  { name: 'Wholesale', type: PriceType.sales },
]

const mockProducts = [
  {
    name: 'Product 1',
    units: [
      {
        name: 'pc',
        multiplier: 1,
        barcode: '************1',
        parent_barcode: null,
      },
      {
        name: 'box',
        multiplier: 6,
        barcode: '*************',
        parent_barcode: '************1',
      },
      {
        name: 'case',
        multiplier: 4,
        barcode: '*************',
        parent_barcode: '*************',
      },
    ],
  },
  {
    name: 'Product 2',
    units: [
      {
        name: 'pc',
        multiplier: 1,
        barcode: '************2',
        parent_barcode: null,
      },
      {
        name: 'pack',
        multiplier: 12,
        barcode: '*************',
        parent_barcode: '************2',
      },
      {
        name: 'carton',
        multiplier: 10,
        barcode: '0000000000222',
        parent_barcode: '*************',
      },
    ],
  },
]

export const seedDatabase = async () => {
  const admin = await findOrCreate(
    () => prisma.user.findUnique({ where: { email: ADMIN_EMAIL } }),
    () =>
      prisma.user.create({
        data: {
          email: ADMIN_EMAIL,
          password: hashPassword(ADMIN_PASSWORD),
          is_active: true,
        },
      }),
  )
  if (NODE_ENV !== 'development') return
  for (const mockCompany of mockCompanies) {
    const existingMockCompany = await prisma.company.findFirst({
      where: {
        name: mockCompany.name,
        permissions: { some: { user_id: admin.id } },
      },
    })
    if (!existingMockCompany) {
      const createdMockCompany = await prisma.company.create({
        data: {
          name: mockCompany.name,
          permissions: { create: { user_id: admin.id } },
        },
      })
      for (const mockBusinessParty of mockBusinessParties) {
        await prisma.businessParty.create({
          data: {
            company_id: createdMockCompany.id,
            name: mockBusinessParty.name,
            is_supplier: mockBusinessParty.is_supplier,
            is_customer: mockBusinessParty.is_customer,
          },
        })
      }
      for (const mockWarehouse of mockWarehouses) {
        await prisma.warehouse.create({
          data: { company_id: createdMockCompany.id, name: mockWarehouse.name },
        })
      }
      for (const mockProduct of mockProducts) {
        const product = await prisma.product.create({
          data: { company_id: createdMockCompany.id, name: mockProduct.name },
        })

        for (const mockUnit of mockProduct.units) {
          let parent = null
          if (mockUnit.parent_barcode) {
            parent = await prisma.productUnit.findFirst({
              where: {
                company_id: createdMockCompany.id,
                barcode: mockUnit.parent_barcode,
              },
            })
          }

          await prisma.productUnit.create({
            data: {
              company_id: createdMockCompany.id,
              product_id: product.id,
              parent_id: parent?.id || null,
              name: mockUnit.name,
              multiplier: mockUnit.multiplier,
              barcode: mockUnit.barcode,
            },
          })
        }
      }
      for (const mockPaymentAccount of mockPaymentAccounts) {
        await prisma.paymentAccount.create({
          data: {
            company_id: createdMockCompany.id,
            name: mockPaymentAccount.name,
            type: mockPaymentAccount.type,
          },
        })
      }
      for (const mockPrice of mockPrices) {
        await prisma.price.create({
          data: {
            company_id: createdMockCompany.id,
            name: mockPrice.name,
            type: mockPrice.type,
          },
        })
      }
    }
  }
}
