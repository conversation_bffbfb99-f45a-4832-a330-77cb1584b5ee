import findMyWay from 'find-my-way'
import formidable from 'formidable'
import { type IncomingMessage, type ServerResponse } from 'node:http'
import readline from 'node:readline'
import { PassThrough, Writable } from 'node:stream'
import { BASE_PATH } from './constants.js'
import { prisma } from './prisma.js'
import { handler as trpcHandler } from './trpc/handler.js'

export const router = findMyWay()

router.all(`${BASE_PATH}*`, trpcHandler)

const parseBarcode = (gs1: string) => gs1.substring(3, 16)

const sendJson = (
  response: ServerResponse,
  statusCode: number,
  payload: unknown,
) => {
  response.writeHead(statusCode, { 'Content-Type': 'application/json' })
  response.end(JSON.stringify(payload))
}

const parseLines = (request: IncomingMessage) => {
  const passThrough = new PassThrough()
  const lineReader = readline.createInterface({
    input: passThrough,
    crlfDelay: Infinity,
  })
  const form = formidable({
    maxFiles: 1,
    maxFileSize: 10 * 1024 * 1024,
    filter: ({ mimetype }) => mimetype === 'text/csv',
    fileWriteStreamHandler: () => {
      return new Writable({
        write: (chunk, encoding, callback) => {
          passThrough.write(chunk, encoding, callback)
        },
      })
    },
  })
  form.parse(request, error => {
    if (error) lineReader.emit('error', error)
    passThrough.end()
  })
  return lineReader
}

router.post('/datamatrix-responses/:id', async (request, response, params) => {
  try {
    const id = parseInt(params.id!)
    const datamatrixResponse =
      await prisma.datamatrixResponse.findUniqueOrThrow({ where: { id } })
    const lines = parseLines(request)
    for await (const line of lines) {
      const barcode = parseBarcode(line)
      const productUnit = await prisma.productUnit.findFirst({
        where: { company_id: datamatrixResponse.company_id, barcode },
      })
      if (!productUnit) continue
      await prisma.datamatrixResponseItem.create({
        data: {
          response_id: id,
          product_unit_id: productUnit.id,
          datamatrix: line,
        },
      })
    }
    sendJson(response, 200, { message: 'File processed successfully.' })
  } catch (error) {
    console.error(error)
    sendJson(response, 500, { message: 'Something went wrong.' })
  }
})
