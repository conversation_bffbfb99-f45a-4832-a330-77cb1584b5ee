-- CreateTable
CREATE TABLE "public"."price_adjustments" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "date" DATE NOT NULL,
  CONSTRAINT "price_adjustments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."price_adjustment_items" (
  "id" SERIAL NOT NULL,
  "adjustment_id" INTEGER NOT NULL,
  "product_unit_id" INTEGER NOT NULL,
  "price" INTEGER NOT NULL,
  CONSTRAINT "price_adjustment_items_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "price_adjustments_company_id_date_idx" ON "public"."price_adjustments" ("company_id", "date");

-- CreateIndex
CREATE INDEX "price_adjustment_items_adjustment_id_idx" ON "public"."price_adjustment_items" ("adjustment_id");

-- CreateIndex
CREATE INDEX "price_adjustment_items_product_unit_id_idx" ON "public"."price_adjustment_items" ("product_unit_id");

-- AddForeignKey
ALTER TABLE "public"."price_adjustments"
ADD CONSTRAINT "price_adjustments_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."price_adjustment_items"
ADD CONSTRAINT "price_adjustment_items_adjustment_id_fkey" FOREIGN KEY ("adjustment_id") REFERENCES "public"."price_adjustments" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."price_adjustment_items"
ADD CONSTRAINT "price_adjustment_items_product_unit_id_fkey" FOREIGN KEY ("product_unit_id") REFERENCES "public"."product_units" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
