-- CreateEnum
CREATE TYPE "public"."PaymentDirection" AS ENUM('incoming', 'outgoing');

-- <PERSON>reate<PERSON>num
CREATE TYPE "public"."PaymentAccountType" AS ENUM('bank', 'cash');

-- CreateTable
CREATE TABLE "public"."business_parties" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "name" TEXT NOT NULL,
  "is_supplier" BOOLEAN NOT NULL,
  "is_customer" BOOLEAN NOT NULL,
  CONSTRAINT "business_parties_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."business_party_inventories" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "business_party_id" INTEGER NOT NULL,
  "amount" DECIMAL(15, 2) NOT NULL,
  CONSTRAINT "business_party_inventories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."companies" (
  "id" SERIAL NOT NULL,
  "name" TEXT NOT NULL,
  CONSTRAINT "companies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."datamatrix_requests" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  CONSTRAINT "datamatrix_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."datamatrix_request_items" (
  "id" SERIAL NOT NULL,
  "datamatrix_request_id" INTEGER NOT NULL,
  "quantity" INTEGER NOT NULL,
  "product_unit_id" INTEGER NOT NULL,
  CONSTRAINT "datamatrix_request_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."datamatrix_responses" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  CONSTRAINT "datamatrix_responses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."datamatrix_response_items" (
  "id" SERIAL NOT NULL,
  "datamatrix_response_id" INTEGER NOT NULL,
  "container_id" INTEGER,
  "product_unit_id" INTEGER NOT NULL,
  "datamatrix" TEXT NOT NULL,
  CONSTRAINT "datamatrix_response_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."money_transactions" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "business_party_id" INTEGER NOT NULL,
  "payment_account_id" INTEGER,
  "amount" INTEGER NOT NULL,
  "date" DATE NOT NULL,
  "payment_id" INTEGER,
  "purchase_invoice_id" INTEGER,
  "sales_invoice_id" INTEGER,
  CONSTRAINT "money_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."payments" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "business_party_id" INTEGER NOT NULL,
  "payment_account_id" INTEGER,
  "amount" INTEGER NOT NULL,
  "date" DATE NOT NULL,
  "direction" "public"."PaymentDirection" NOT NULL,
  CONSTRAINT "payments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."payment_accounts" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "name" TEXT NOT NULL,
  "type" "public"."PaymentAccountType" NOT NULL,
  CONSTRAINT "payment_accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."permissions" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "user_id" INTEGER NOT NULL,
  CONSTRAINT "permissions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."products" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "name" TEXT NOT NULL,
  CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_inventories" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "warehouse_id" INTEGER NOT NULL,
  "date" DATE NOT NULL,
  "total" INTEGER NOT NULL,
  CONSTRAINT "product_inventories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_inventory_items" (
  "id" SERIAL NOT NULL,
  "inventory_id" INTEGER NOT NULL,
  "product_unit_id" INTEGER NOT NULL,
  "amount" INTEGER NOT NULL,
  "price" INTEGER NOT NULL,
  CONSTRAINT "product_inventory_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_transactions" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "warehouse_id" INTEGER NOT NULL,
  "product_unit_id" INTEGER NOT NULL,
  "quantity" INTEGER NOT NULL,
  "price" INTEGER NOT NULL,
  "date" DATE NOT NULL,
  "product_inventory_item_id" INTEGER,
  "purchase_invoice_item_id" INTEGER,
  "sales_invoice_item_id" INTEGER,
  "warehouse_transfer_item_id" INTEGER,
  CONSTRAINT "product_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."product_units" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "product_id" INTEGER NOT NULL,
  "parent_id" INTEGER,
  "name" TEXT NOT NULL,
  "multiplier" INTEGER NOT NULL,
  "barcode" TEXT NOT NULL,
  CONSTRAINT "product_units_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."purchase_invoices" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "supplier_id" INTEGER NOT NULL,
  "warehouse_id" INTEGER NOT NULL,
  "date" DATE NOT NULL,
  "total" INTEGER NOT NULL,
  CONSTRAINT "purchase_invoices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."purchase_invoice_items" (
  "id" SERIAL NOT NULL,
  "invoice_id" INTEGER NOT NULL,
  "product_unit_id" INTEGER NOT NULL,
  "amount" INTEGER NOT NULL,
  "price" INTEGER NOT NULL,
  CONSTRAINT "purchase_invoice_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."sales_invoices" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "customer_id" INTEGER NOT NULL,
  "warehouse_id" INTEGER NOT NULL,
  "date" DATE NOT NULL,
  "total" INTEGER NOT NULL,
  CONSTRAINT "sales_invoices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."sales_invoice_items" (
  "id" SERIAL NOT NULL,
  "invoice_id" INTEGER NOT NULL,
  "product_unit_id" INTEGER NOT NULL,
  "amount" INTEGER NOT NULL,
  "price" INTEGER NOT NULL,
  CONSTRAINT "sales_invoice_items_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."tokens" (
  "id" SERIAL NOT NULL,
  "user_id" INTEGER NOT NULL,
  "value" TEXT NOT NULL,
  CONSTRAINT "tokens_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."users" (
  "id" SERIAL NOT NULL,
  "is_active" BOOLEAN NOT NULL,
  "email" TEXT NOT NULL,
  "password" TEXT NOT NULL,
  CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."warehouses" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "name" TEXT NOT NULL,
  CONSTRAINT "warehouses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."warehouse_transfers" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "from_warehouse_id" INTEGER NOT NULL,
  "to_warehouse_id" INTEGER NOT NULL,
  "date" DATE NOT NULL,
  "total" INTEGER NOT NULL,
  CONSTRAINT "warehouse_transfers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."warehouse_transfer_items" (
  "id" SERIAL NOT NULL,
  "transfer_id" INTEGER NOT NULL,
  "product_unit_id" INTEGER NOT NULL,
  "amount" INTEGER NOT NULL,
  "price" INTEGER NOT NULL,
  CONSTRAINT "warehouse_transfer_items_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "business_parties_company_id_idx" ON "public"."business_parties" ("company_id");

-- CreateIndex
CREATE INDEX "business_parties_company_id_is_supplier_idx" ON "public"."business_parties" ("company_id", "is_supplier");

-- CreateIndex
CREATE INDEX "business_parties_company_id_is_customer_idx" ON "public"."business_parties" ("company_id", "is_customer");

-- CreateIndex
CREATE INDEX "business_party_inventories_business_party_id_idx" ON "public"."business_party_inventories" ("business_party_id");

-- CreateIndex
CREATE UNIQUE INDEX "business_party_inventories_company_id_business_party_id_key" ON "public"."business_party_inventories" ("company_id", "business_party_id");

-- CreateIndex
CREATE INDEX "datamatrix_requests_company_id_idx" ON "public"."datamatrix_requests" ("company_id");

-- CreateIndex
CREATE INDEX "datamatrix_request_items_datamatrix_request_id_idx" ON "public"."datamatrix_request_items" ("datamatrix_request_id");

-- CreateIndex
CREATE INDEX "datamatrix_request_items_product_unit_id_idx" ON "public"."datamatrix_request_items" ("product_unit_id");

-- CreateIndex
CREATE INDEX "datamatrix_responses_company_id_idx" ON "public"."datamatrix_responses" ("company_id");

-- CreateIndex
CREATE UNIQUE INDEX "datamatrix_response_items_datamatrix_key" ON "public"."datamatrix_response_items" ("datamatrix");

-- CreateIndex
CREATE INDEX "datamatrix_response_items_datamatrix_response_id_idx" ON "public"."datamatrix_response_items" ("datamatrix_response_id");

-- CreateIndex
CREATE INDEX "datamatrix_response_items_container_id_idx" ON "public"."datamatrix_response_items" ("container_id");

-- CreateIndex
CREATE INDEX "datamatrix_response_items_product_unit_id_idx" ON "public"."datamatrix_response_items" ("product_unit_id");

-- CreateIndex
CREATE INDEX "money_transactions_company_id_date_idx" ON "public"."money_transactions" ("company_id", "date");

-- CreateIndex
CREATE INDEX "money_transactions_business_party_id_idx" ON "public"."money_transactions" ("business_party_id");

-- CreateIndex
CREATE INDEX "money_transactions_payment_account_id_idx" ON "public"."money_transactions" ("payment_account_id");

-- CreateIndex
CREATE INDEX "money_transactions_payment_id_idx" ON "public"."money_transactions" ("payment_id");

-- CreateIndex
CREATE INDEX "money_transactions_purchase_invoice_id_idx" ON "public"."money_transactions" ("purchase_invoice_id");

-- CreateIndex
CREATE INDEX "money_transactions_sales_invoice_id_idx" ON "public"."money_transactions" ("sales_invoice_id");

-- CreateIndex
CREATE INDEX "payments_company_id_date_idx" ON "public"."payments" ("company_id", "date");

-- CreateIndex
CREATE INDEX "payments_business_party_id_idx" ON "public"."payments" ("business_party_id");

-- CreateIndex
CREATE INDEX "payments_payment_account_id_idx" ON "public"."payments" ("payment_account_id");

-- CreateIndex
CREATE INDEX "payment_accounts_company_id_idx" ON "public"."payment_accounts" ("company_id");

-- CreateIndex
CREATE INDEX "permissions_user_id_idx" ON "public"."permissions" ("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "permissions_company_id_user_id_key" ON "public"."permissions" ("company_id", "user_id");

-- CreateIndex
CREATE INDEX "products_company_id_idx" ON "public"."products" ("company_id");

-- CreateIndex
CREATE INDEX "product_inventories_company_id_date_idx" ON "public"."product_inventories" ("company_id", "date");

-- CreateIndex
CREATE INDEX "product_inventories_warehouse_id_idx" ON "public"."product_inventories" ("warehouse_id");

-- CreateIndex
CREATE INDEX "product_inventory_items_inventory_id_idx" ON "public"."product_inventory_items" ("inventory_id");

-- CreateIndex
CREATE INDEX "product_inventory_items_product_unit_id_idx" ON "public"."product_inventory_items" ("product_unit_id");

-- CreateIndex
CREATE INDEX "product_transactions_company_id_date_idx" ON "public"."product_transactions" ("company_id", "date");

-- CreateIndex
CREATE INDEX "product_transactions_warehouse_id_idx" ON "public"."product_transactions" ("warehouse_id");

-- CreateIndex
CREATE INDEX "product_transactions_product_unit_id_idx" ON "public"."product_transactions" ("product_unit_id");

-- CreateIndex
CREATE INDEX "product_transactions_product_inventory_item_id_idx" ON "public"."product_transactions" ("product_inventory_item_id");

-- CreateIndex
CREATE INDEX "product_transactions_purchase_invoice_item_id_idx" ON "public"."product_transactions" ("purchase_invoice_item_id");

-- CreateIndex
CREATE INDEX "product_transactions_sales_invoice_item_id_idx" ON "public"."product_transactions" ("sales_invoice_item_id");

-- CreateIndex
CREATE INDEX "product_transactions_warehouse_transfer_item_id_idx" ON "public"."product_transactions" ("warehouse_transfer_item_id");

-- CreateIndex
CREATE INDEX "product_units_company_id_product_id_idx" ON "public"."product_units" ("company_id", "product_id");

-- CreateIndex
CREATE INDEX "product_units_parent_id_idx" ON "public"."product_units" ("parent_id");

-- CreateIndex
CREATE UNIQUE INDEX "product_units_company_id_barcode_key" ON "public"."product_units" ("company_id", "barcode");

-- CreateIndex
CREATE INDEX "purchase_invoices_company_id_date_idx" ON "public"."purchase_invoices" ("company_id", "date");

-- CreateIndex
CREATE INDEX "purchase_invoices_supplier_id_idx" ON "public"."purchase_invoices" ("supplier_id");

-- CreateIndex
CREATE INDEX "purchase_invoices_warehouse_id_idx" ON "public"."purchase_invoices" ("warehouse_id");

-- CreateIndex
CREATE INDEX "purchase_invoice_items_invoice_id_idx" ON "public"."purchase_invoice_items" ("invoice_id");

-- CreateIndex
CREATE INDEX "purchase_invoice_items_product_unit_id_idx" ON "public"."purchase_invoice_items" ("product_unit_id");

-- CreateIndex
CREATE INDEX "sales_invoices_company_id_date_idx" ON "public"."sales_invoices" ("company_id", "date");

-- CreateIndex
CREATE INDEX "sales_invoices_customer_id_idx" ON "public"."sales_invoices" ("customer_id");

-- CreateIndex
CREATE INDEX "sales_invoices_warehouse_id_idx" ON "public"."sales_invoices" ("warehouse_id");

-- CreateIndex
CREATE INDEX "sales_invoice_items_invoice_id_idx" ON "public"."sales_invoice_items" ("invoice_id");

-- CreateIndex
CREATE INDEX "sales_invoice_items_product_unit_id_idx" ON "public"."sales_invoice_items" ("product_unit_id");

-- CreateIndex
CREATE UNIQUE INDEX "tokens_value_key" ON "public"."tokens" ("value");

-- CreateIndex
CREATE INDEX "tokens_user_id_idx" ON "public"."tokens" ("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "public"."users" ("email");

-- CreateIndex
CREATE INDEX "warehouses_company_id_idx" ON "public"."warehouses" ("company_id");

-- CreateIndex
CREATE INDEX "warehouse_transfers_company_id_date_idx" ON "public"."warehouse_transfers" ("company_id", "date");

-- CreateIndex
CREATE INDEX "warehouse_transfers_from_warehouse_id_idx" ON "public"."warehouse_transfers" ("from_warehouse_id");

-- CreateIndex
CREATE INDEX "warehouse_transfers_to_warehouse_id_idx" ON "public"."warehouse_transfers" ("to_warehouse_id");

-- CreateIndex
CREATE INDEX "warehouse_transfer_items_transfer_id_idx" ON "public"."warehouse_transfer_items" ("transfer_id");

-- CreateIndex
CREATE INDEX "warehouse_transfer_items_product_unit_id_idx" ON "public"."warehouse_transfer_items" ("product_unit_id");

-- AddForeignKey
ALTER TABLE "public"."business_parties"
ADD CONSTRAINT "business_parties_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."business_party_inventories"
ADD CONSTRAINT "business_party_inventories_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."business_party_inventories"
ADD CONSTRAINT "business_party_inventories_business_party_id_fkey" FOREIGN KEY ("business_party_id") REFERENCES "public"."business_parties" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."datamatrix_requests"
ADD CONSTRAINT "datamatrix_requests_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."datamatrix_request_items"
ADD CONSTRAINT "datamatrix_request_items_datamatrix_request_id_fkey" FOREIGN KEY ("datamatrix_request_id") REFERENCES "public"."datamatrix_requests" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."datamatrix_request_items"
ADD CONSTRAINT "datamatrix_request_items_product_unit_id_fkey" FOREIGN KEY ("product_unit_id") REFERENCES "public"."product_units" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."datamatrix_responses"
ADD CONSTRAINT "datamatrix_responses_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."datamatrix_response_items"
ADD CONSTRAINT "datamatrix_response_items_datamatrix_response_id_fkey" FOREIGN KEY ("datamatrix_response_id") REFERENCES "public"."datamatrix_responses" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."datamatrix_response_items"
ADD CONSTRAINT "datamatrix_response_items_container_id_fkey" FOREIGN KEY ("container_id") REFERENCES "public"."datamatrix_response_items" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."datamatrix_response_items"
ADD CONSTRAINT "datamatrix_response_items_product_unit_id_fkey" FOREIGN KEY ("product_unit_id") REFERENCES "public"."product_units" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."money_transactions"
ADD CONSTRAINT "money_transactions_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."money_transactions"
ADD CONSTRAINT "money_transactions_business_party_id_fkey" FOREIGN KEY ("business_party_id") REFERENCES "public"."business_parties" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."money_transactions"
ADD CONSTRAINT "money_transactions_payment_account_id_fkey" FOREIGN KEY ("payment_account_id") REFERENCES "public"."payment_accounts" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."money_transactions"
ADD CONSTRAINT "money_transactions_payment_id_fkey" FOREIGN KEY ("payment_id") REFERENCES "public"."payments" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."money_transactions"
ADD CONSTRAINT "money_transactions_purchase_invoice_id_fkey" FOREIGN KEY ("purchase_invoice_id") REFERENCES "public"."purchase_invoices" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."money_transactions"
ADD CONSTRAINT "money_transactions_sales_invoice_id_fkey" FOREIGN KEY ("sales_invoice_id") REFERENCES "public"."sales_invoices" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."payments"
ADD CONSTRAINT "payments_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."payments"
ADD CONSTRAINT "payments_business_party_id_fkey" FOREIGN KEY ("business_party_id") REFERENCES "public"."business_parties" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."payments"
ADD CONSTRAINT "payments_payment_account_id_fkey" FOREIGN KEY ("payment_account_id") REFERENCES "public"."payment_accounts" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."payment_accounts"
ADD CONSTRAINT "payment_accounts_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."permissions"
ADD CONSTRAINT "permissions_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."permissions"
ADD CONSTRAINT "permissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."products"
ADD CONSTRAINT "products_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_inventories"
ADD CONSTRAINT "product_inventories_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_inventories"
ADD CONSTRAINT "product_inventories_warehouse_id_fkey" FOREIGN KEY ("warehouse_id") REFERENCES "public"."warehouses" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_inventory_items"
ADD CONSTRAINT "product_inventory_items_inventory_id_fkey" FOREIGN KEY ("inventory_id") REFERENCES "public"."product_inventories" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_inventory_items"
ADD CONSTRAINT "product_inventory_items_product_unit_id_fkey" FOREIGN KEY ("product_unit_id") REFERENCES "public"."product_units" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_transactions"
ADD CONSTRAINT "product_transactions_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_transactions"
ADD CONSTRAINT "product_transactions_warehouse_id_fkey" FOREIGN KEY ("warehouse_id") REFERENCES "public"."warehouses" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_transactions"
ADD CONSTRAINT "product_transactions_product_unit_id_fkey" FOREIGN KEY ("product_unit_id") REFERENCES "public"."product_units" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_transactions"
ADD CONSTRAINT "product_transactions_product_inventory_item_id_fkey" FOREIGN KEY ("product_inventory_item_id") REFERENCES "public"."product_inventory_items" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_transactions"
ADD CONSTRAINT "product_transactions_purchase_invoice_item_id_fkey" FOREIGN KEY ("purchase_invoice_item_id") REFERENCES "public"."purchase_invoice_items" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_transactions"
ADD CONSTRAINT "product_transactions_sales_invoice_item_id_fkey" FOREIGN KEY ("sales_invoice_item_id") REFERENCES "public"."sales_invoice_items" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_transactions"
ADD CONSTRAINT "product_transactions_warehouse_transfer_item_id_fkey" FOREIGN KEY ("warehouse_transfer_item_id") REFERENCES "public"."warehouse_transfer_items" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_units"
ADD CONSTRAINT "product_units_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_units"
ADD CONSTRAINT "product_units_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_units"
ADD CONSTRAINT "product_units_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."product_units" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."purchase_invoices"
ADD CONSTRAINT "purchase_invoices_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."purchase_invoices"
ADD CONSTRAINT "purchase_invoices_supplier_id_fkey" FOREIGN KEY ("supplier_id") REFERENCES "public"."business_parties" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."purchase_invoices"
ADD CONSTRAINT "purchase_invoices_warehouse_id_fkey" FOREIGN KEY ("warehouse_id") REFERENCES "public"."warehouses" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."purchase_invoice_items"
ADD CONSTRAINT "purchase_invoice_items_invoice_id_fkey" FOREIGN KEY ("invoice_id") REFERENCES "public"."purchase_invoices" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."purchase_invoice_items"
ADD CONSTRAINT "purchase_invoice_items_product_unit_id_fkey" FOREIGN KEY ("product_unit_id") REFERENCES "public"."product_units" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."sales_invoices"
ADD CONSTRAINT "sales_invoices_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."sales_invoices"
ADD CONSTRAINT "sales_invoices_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "public"."business_parties" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."sales_invoices"
ADD CONSTRAINT "sales_invoices_warehouse_id_fkey" FOREIGN KEY ("warehouse_id") REFERENCES "public"."warehouses" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."sales_invoice_items"
ADD CONSTRAINT "sales_invoice_items_invoice_id_fkey" FOREIGN KEY ("invoice_id") REFERENCES "public"."sales_invoices" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."sales_invoice_items"
ADD CONSTRAINT "sales_invoice_items_product_unit_id_fkey" FOREIGN KEY ("product_unit_id") REFERENCES "public"."product_units" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."tokens"
ADD CONSTRAINT "tokens_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."warehouses"
ADD CONSTRAINT "warehouses_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."warehouse_transfers"
ADD CONSTRAINT "warehouse_transfers_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."warehouse_transfers"
ADD CONSTRAINT "warehouse_transfers_from_warehouse_id_fkey" FOREIGN KEY ("from_warehouse_id") REFERENCES "public"."warehouses" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."warehouse_transfers"
ADD CONSTRAINT "warehouse_transfers_to_warehouse_id_fkey" FOREIGN KEY ("to_warehouse_id") REFERENCES "public"."warehouses" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."warehouse_transfer_items"
ADD CONSTRAINT "warehouse_transfer_items_transfer_id_fkey" FOREIGN KEY ("transfer_id") REFERENCES "public"."warehouse_transfers" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."warehouse_transfer_items"
ADD CONSTRAINT "warehouse_transfer_items_product_unit_id_fkey" FOREIGN KEY ("product_unit_id") REFERENCES "public"."product_units" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
