-- CreateTable
CREATE TABLE "public"."product_stocks" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "warehouse_id" INTEGER NOT NULL,
  "product_unit_id" INTEGER NOT NULL,
  "quantity" INTEGER NOT NULL,
  CONSTRAINT "product_stocks_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "product_stocks_company_id_warehouse_id_product_unit_id_key" ON "public"."product_stocks" ("company_id", "warehouse_id", "product_unit_id");

-- AddForeignKey
ALTER TABLE "public"."product_stocks"
ADD CONSTRAINT "product_stocks_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_stocks"
ADD CONSTRAINT "product_stocks_warehouse_id_fkey" FOREIGN KEY ("warehouse_id") REFERENCES "public"."warehouses" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."product_stocks"
ADD CONSTRAINT "product_stocks_product_unit_id_fkey" FOREIGN KEY ("product_unit_id") REFERENCES "public"."product_units" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
