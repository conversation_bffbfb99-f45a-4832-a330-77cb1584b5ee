-- CreateEnum
CREATE TYPE "public"."PriceType" AS ENUM('purchase', 'sales');

-- CreateTable
CREATE TABLE "public"."prices" (
  "id" SERIAL NOT NULL,
  "company_id" INTEGER NOT NULL,
  "name" TEXT NOT NULL,
  "type" "public"."PriceType" NOT NULL,
  CONSTRAINT "prices_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "prices_company_id_idx" ON "public"."prices" ("company_id");

-- AddForeignKey
ALTER TABLE "public"."prices"
ADD CONSTRAINT "prices_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."companies" ("id") ON DELETE RESTRICT ON UPDATE CASCADE;
