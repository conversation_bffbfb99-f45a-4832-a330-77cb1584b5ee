services:
  dbgate:
    image: docker.io/dbgate/dbgate:latest
    environment:
      CONNECTIONS: CN1
      LABEL_CN1: postgres://postgres@postgres:5432/postgres?sslmode=disable
      SERVER_CN1: postgres
      USER_CN1: postgres
      PASSWORD_CN1: postgres
      PORT_CN1: 5432
      ENGINE_CN1: postgres@dbgate-plugin-postgres
    ports:
      - '127.0.0.1:3000:3000'
  postgres:
    image: docker.io/postgres:latest
    environment:
      POSTGRES_PASSWORD: postgres
    ports:
      - '127.0.0.1:5432:5432'
    command: postgres -c log_statement=all
  redis:
    image: docker.io/redis:latest
    ports:
      - '127.0.0.1:6379:6379'
  nats:
    image: docker.io/nats:latest
    command: -js -m 8222
    ports:
      - '127.0.0.1:4222:4222'
      - '127.0.0.1:8222:8222'
