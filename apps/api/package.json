{"name": "@cloudretail/api", "version": "1.0.0", "private": true, "type": "module", "exports": "./src/exports.ts", "scripts": {"build": "tsc --outDir dist", "compose:down": "docker compose down --remove-orphans --volumes", "compose:up": "docker compose up -d", "lint": "DEBUG=eslint:cli-engine eslint --fix .", "prepare": "prisma generate", "start": "dotenv -c -- npx tsx watch --clear-screen=false src/index.ts", "test": "vitest", "typecheck": "tsc --noEmit", "typecheck:w": "tsc --noEmit --preserveWatchOutput --watch"}, "dependencies": {"@prisma/client": "~6.16.2", "@trpc/server": "~11.5.1", "date-fns": "~4.1.0", "dotenv-cli": "~10.0.0", "find-my-way": "~9.3.0", "formidable": "~3.5.4", "nats": "~2.29.3", "nodemailer": "~7.0.6", "pg": "~8.16.3", "redis": "~5.8.2", "remeda": "~2.32.0", "zod": "~4.1.9", "zx": "~8.8.1"}, "devDependencies": {"@eslint/js": "~9.36.0", "@types/formidable": "~3.4.5", "@types/node": "~24.5.2", "@types/nodemailer": "~7.0.1", "@types/pg": "~8.15.5", "eslint": "~9.36.0", "libpg-query": "~17.6.0", "npm-run-all": "~4.1.5", "prisma": "~6.16.2", "tsx": "~4.20.5", "typescript": "~5.9.2", "typescript-eslint": "~8.44.0", "vitest": "~3.2.4"}}