import { describe, expect, it } from 'vitest'
import {
  formatNumber,
  majorToMinor,
  minorToMajor,
  parseMajorToMinor,
} from './numbers.js'

describe('formatNumber', () => {
  it('formats whole numbers correctly', () => {
    expect(formatNumber(1)).toBe('1.00')
    expect(formatNumber(10)).toBe('10.00')
    expect(formatNumber(100)).toBe('100.00')
    expect(formatNumber(1000)).toBe('1,000.00')
  })

  it('formats decimal numbers correctly', () => {
    expect(formatNumber(1.23)).toBe('1.23')
    expect(formatNumber(10.99)).toBe('10.99')
    expect(formatNumber(0.01)).toBe('0.01')
    expect(formatNumber(0.99)).toBe('0.99')
  })

  it('formats large numbers with grouping', () => {
    expect(formatNumber(1000)).toBe('1,000.00')
    expect(formatNumber(10000)).toBe('10,000.00')
    expect(formatNumber(100000)).toBe('100,000.00')
    expect(formatNumber(1000000)).toBe('1,000,000.00')
    expect(formatNumber(1234567.89)).toBe('1,234,567.89')
  })

  it('handles zero correctly', () => {
    expect(formatNumber(0)).toBe('0.00')
  })

  it('handles negative numbers correctly', () => {
    expect(formatNumber(-1)).toBe('-1.00')
    expect(formatNumber(-1.23)).toBe('-1.23')
    expect(formatNumber(-1000)).toBe('-1,000.00')
    expect(formatNumber(-1234567.89)).toBe('-1,234,567.89')
  })

  it('always shows two decimal places', () => {
    expect(formatNumber(5)).toBe('5.00')
    expect(formatNumber(5.5)).toBe('5.50')
    expect(formatNumber(5.05)).toBe('5.05')
    expect(formatNumber(100.1)).toBe('100.10')
  })

  it('rounds to two decimal places when needed', () => {
    expect(formatNumber(1.234)).toBe('1.23')
    expect(formatNumber(1.235)).toBe('1.24')
    expect(formatNumber(1.999)).toBe('2.00')
    expect(formatNumber(0.999)).toBe('1.00')
  })

  it('handles very small numbers', () => {
    expect(formatNumber(0.001)).toBe('0.00')
    expect(formatNumber(0.004)).toBe('0.00')
    expect(formatNumber(0.005)).toBe('0.01')
    expect(formatNumber(0.009)).toBe('0.01')
  })

  it('handles very large numbers', () => {
    expect(formatNumber(999999999)).toBe('999,999,999.00')
    expect(formatNumber(1000000000)).toBe('1,000,000,000.00')
    expect(formatNumber(1234567890.12)).toBe('1,234,567,890.12')
  })

  it('handles floating point precision issues', () => {
    expect(formatNumber(0.1 + 0.2)).toBe('0.30')
    expect(formatNumber(1.005)).toBe('1.01')
    expect(formatNumber(1.015)).toBe('1.02')
  })

  it('handles edge cases with infinity and NaN gracefully', () => {
    expect(formatNumber(Infinity)).toMatch(/∞|Infinity/)
    expect(formatNumber(-Infinity)).toMatch(/-∞|-Infinity/)
    expect(formatNumber(NaN)).toBe('NaN')
  })

  it('formats fractional numbers consistently', () => {
    expect(formatNumber(1.1)).toBe('1.10')
    expect(formatNumber(1.01)).toBe('1.01')
    expect(formatNumber(1.001)).toBe('1.00')
    expect(formatNumber(1.999)).toBe('2.00')
  })
})

describe('minorToMajor', () => {
  it('converts cents to dollars correctly', () => {
    expect(minorToMajor(100)).toBe(1)
    expect(minorToMajor(1000)).toBe(10)
    expect(minorToMajor(10000)).toBe(100)
  })

  it('converts partial cents correctly', () => {
    expect(minorToMajor(123)).toBe(1.23)
    expect(minorToMajor(1099)).toBe(10.99)
    expect(minorToMajor(1)).toBe(0.01)
    expect(minorToMajor(99)).toBe(0.99)
  })

  it('handles zero correctly', () => {
    expect(minorToMajor(0)).toBe(0)
  })

  it('handles negative amounts correctly', () => {
    expect(minorToMajor(-123)).toBe(-1.23)
    expect(minorToMajor(-1000)).toBe(-10)
  })
})

describe('majorToMinor', () => {
  it('converts whole dollars to cents correctly', () => {
    expect(majorToMinor(1)).toBe(100)
    expect(majorToMinor(10)).toBe(1000)
    expect(majorToMinor(100)).toBe(10000)
  })

  it('converts decimal amounts correctly', () => {
    expect(majorToMinor(1.23)).toBe(123)
    expect(majorToMinor(10.99)).toBe(1099)
    expect(majorToMinor(0.01)).toBe(1)
    expect(majorToMinor(0.99)).toBe(99)
  })

  it('rounds to nearest cent for precision issues', () => {
    expect(majorToMinor(1.235)).toBe(124)
    expect(majorToMinor(1.234)).toBe(123)
    expect(majorToMinor(0.999)).toBe(100)
  })

  it('handles zero correctly', () => {
    expect(majorToMinor(0)).toBe(0)
  })

  it('handles negative amounts correctly', () => {
    expect(majorToMinor(-1.23)).toBe(-123)
    expect(majorToMinor(-10)).toBe(-1000)
  })

  it('handles floating point precision issues', () => {
    expect(majorToMinor(0.1 + 0.2)).toBe(30)
    expect(majorToMinor(1.005)).toBe(100)
  })
})

describe('parseMajorToMinor', () => {
  it('parses valid numeric strings correctly', () => {
    expect(parseMajorToMinor('1.23')).toBe(123)
    expect(parseMajorToMinor('10.99')).toBe(1099)
    expect(parseMajorToMinor('100')).toBe(10000)
    expect(parseMajorToMinor('0.01')).toBe(1)
  })

  it('handles empty string as zero', () => {
    expect(parseMajorToMinor('')).toBe(0)
  })

  it('handles invalid strings as zero', () => {
    expect(parseMajorToMinor('invalid')).toBe(0)
    expect(parseMajorToMinor('abc')).toBe(0)
    expect(parseMajorToMinor('$10.99')).toBe(0)
  })

  it('handles strings with extra whitespace', () => {
    expect(parseMajorToMinor(' 1.23 ')).toBe(123)
    expect(parseMajorToMinor('\t10.99\n')).toBe(1099)
  })

  it('handles negative values', () => {
    expect(parseMajorToMinor('-1.23')).toBe(-123)
    expect(parseMajorToMinor('-10')).toBe(-1000)
  })

  it('handles zero values', () => {
    expect(parseMajorToMinor('0')).toBe(0)
    expect(parseMajorToMinor('0.00')).toBe(0)
  })

  it('handles floating point precision issues', () => {
    expect(parseMajorToMinor('1.235')).toBe(124)
    expect(parseMajorToMinor('1.005')).toBe(100)
  })

  it('handles very small decimal values', () => {
    expect(parseMajorToMinor('0.001')).toBe(0)
    expect(parseMajorToMinor('0.009')).toBe(1)
  })
})

describe('number conversion round-trip', () => {
  it.each([0, 1, 1.23, 10.99, 100, 999.99])(
    'maintains consistency between major/minor conversions for value: %s',
    value => {
      const minor = majorToMinor(value)
      const backToMajor = minorToMajor(minor)
      expect(backToMajor).toBe(value)
    },
  )
})
