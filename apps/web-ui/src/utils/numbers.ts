export const formatNumber = (value: number): string => {
  return value.toLocaleString(undefined, {
    maximumFractionDigits: 2,
    minimumFractionDigits: 2,
    useGrouping: true,
  })
}

export const minorToMajor = (minor: number): number => {
  return minor / 100
}

export const majorToMinor = (major: number): number => {
  return Math.round(major * 100)
}

export const parseMajorToMinor = (majorString: string): number => {
  return majorToMinor(parseFloat(majorString) || 0)
}
