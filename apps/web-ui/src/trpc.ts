import type { AppRouter } from '@cloudretail/api'
import { httpLink } from '@trpc/client'
import { createTRPCReact } from '@trpc/react-query'
import { TOKEN_KEY } from './constants'

export const trpc = createTRPCReact<AppRouter>()
export const trpcClient = trpc.createClient({
  links: [
    httpLink({
      url: '/api/trpc',
      headers() {
        const token = window.localStorage.getItem(TOKEN_KEY)
        return token ? { Authorization: `Bearer ${token}` } : {}
      },
    }),
  ],
})
