import { createBrowserRouter } from 'react-router'
import { paths } from './constants'
import { Dashboard } from './pages/Dashboard'
import { Entry } from './pages/Entry'
import { NotFound } from './pages/NotFound'
import { Register } from './pages/Register'
import authorizedUsersRoute from './pages/authorizedUsers/route'
import businessPartiesRoute from './pages/business-parties/route'
import businessPartyInventoriesRoute from './pages/business-party-inventories/route'
import companiesRoute from './pages/companies/route'
import datamatrixRequestsRoute from './pages/datamatrix-requests/route'
import datamatrixResponsesRoute from './pages/datamatrix-responses/route'
import packagingRoute from './pages/packaging/route'
import paymentAccountsRoute from './pages/payment-accounts/route'
import paymentsRoute from './pages/payments/route'
import priceAdjustmentsRoute from './pages/price-adjustments/route'
import pricesRoute from './pages/prices/route'
import productInventoriesRoute from './pages/product-inventories/route'
import productMovementsRoute from './pages/product-movements/route'
import productsRoute from './pages/products/route'
import purchaseInvoicesRoute from './pages/purchase-invoices/route'
import salesInvoicesRoute from './pages/sales-invoices/route'
import warehouseTransfersRoute from './pages/warehouse-transfers/route'
import warehousesRoute from './pages/warehouses/route'

export const router = createBrowserRouter([
  {
    path: paths.register,
    Component: Entry,
    children: [{ path: '', Component: Register }],
  },
  {
    path: '',
    Component: Dashboard,
    children: [
      authorizedUsersRoute,
      companiesRoute,
      businessPartiesRoute,
      businessPartyInventoriesRoute,
      datamatrixRequestsRoute,
      datamatrixResponsesRoute,
      packagingRoute,
      paymentAccountsRoute,
      paymentsRoute,
      priceAdjustmentsRoute,
      productsRoute,
      productMovementsRoute,
      productInventoriesRoute,
      purchaseInvoicesRoute,
      salesInvoicesRoute,
      warehousesRoute,
      warehouseTransfersRoute,
      pricesRoute,
    ],
  },
  { path: '*', Component: NotFound },
])
