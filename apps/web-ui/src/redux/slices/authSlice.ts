import { TOKEN_KEY } from '@/constants'
import { trpcClient } from '@/trpc'
import {
  createAsyncThunk,
  createSlice,
  type PayloadAction,
} from '@reduxjs/toolkit'

type AuthState = {
  isRestoreTriggered: boolean
  isRestoring: boolean
  isAuthenticating: boolean
  isAuthenticated: boolean
  email: string | null
  token: string | null
}

const initialState: AuthState = {
  isRestoreTriggered: false,
  isRestoring: false,
  isAuthenticating: false,
  isAuthenticated: false,
  email: null,
  token: null,
}

export const restoreToken = createAsyncThunk(
  'auth/restoreToken',
  async (_, { rejectWithValue }) => {
    const tokenValue = window.localStorage.getItem(TOKEN_KEY)
    if (tokenValue === null) {
      throw new Error('No token')
    }
    try {
      const token = await trpcClient.tokens.findOne.query({ value: tokenValue })
      return { email: token.user.email, token: token.value }
    } catch (error) {
      if (error instanceof Error && error.message === 'Unauthorized') {
        window.localStorage.removeItem(TOKEN_KEY)
      }
      return rejectWithValue({
        message: error instanceof Error ? error.message : 'Unknown error',
        name: error instanceof Error ? error.name : 'Error',
      })
    }
  },
)

export const login = createAsyncThunk(
  'auth/login',
  async (
    credentials: { email: string; password: string },
    { rejectWithValue },
  ) => {
    try {
      const token = await trpcClient.tokens.createOne.mutate(credentials)
      window.localStorage.setItem(TOKEN_KEY, token.value)
      return { email: token.user.email, token: token.value }
    } catch (error) {
      return rejectWithValue({
        message: error instanceof Error ? error.message : 'Unknown error',
        name: error instanceof Error ? error.name : 'Error',
      })
    }
  },
)

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState }
      const token = state.auth.token
      if (token !== null) {
        await trpcClient.tokens.deleteOne.mutate({ value: token })
      }
      window.localStorage.removeItem(TOKEN_KEY)
    } catch (error) {
      window.localStorage.removeItem(TOKEN_KEY)
      return rejectWithValue({
        message: error instanceof Error ? error.message : 'Unknown error',
        name: error instanceof Error ? error.name : 'Error',
      })
    }
  },
)

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setIsRestoreTriggered: state => {
      state.isRestoreTriggered = true
    },
    setIsRestoring: (state, { payload }: PayloadAction<boolean>) => {
      state.isRestoring = payload
    },
    setUserInfo: (
      state,
      { payload }: PayloadAction<Pick<AuthState, 'email' | 'token'>>,
    ) => {
      state.isAuthenticating = false
      state.isAuthenticated = true
      state.email = payload.email
      state.token = payload.token
    },
    reset: () => ({ ...initialState, isRestoreTriggered: true }),
  },
  extraReducers: builder => {
    builder
      .addCase(restoreToken.pending, state => {
        state.isRestoreTriggered = true
        state.isRestoring = true
      })
      .addCase(restoreToken.fulfilled, (state, action) => {
        state.isRestoring = false
        state.isAuthenticating = false
        state.isAuthenticated = true
        state.email = action.payload.email
        state.token = action.payload.token!
      })
      .addCase(restoreToken.rejected, state => {
        state.isRestoring = false
        state.isAuthenticating = false
        state.isAuthenticated = false
        state.email = null
        state.token = null
      })
      .addCase(login.pending, state => {
        state.isAuthenticating = true
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isAuthenticating = false
        state.isAuthenticated = true
        state.email = action.payload.email
        state.token = action.payload.token!
      })
      .addCase(login.rejected, state => {
        state.isAuthenticating = false
        state.isAuthenticated = false
        state.email = null
        state.token = null
      })
      .addCase(logout.fulfilled, () => ({
        ...initialState,
        isRestoreTriggered: true,
      }))
  },
})
