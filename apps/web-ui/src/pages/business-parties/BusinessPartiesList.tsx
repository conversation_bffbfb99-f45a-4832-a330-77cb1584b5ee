import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type BusinessParty = RouterOutputs['businessParties']['findMany'][number]

export const BusinessPartiesList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const getBusinessParties = trpc.businessParties.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<BusinessParty>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.name'), cell: row => row.name },
    {
      title: t('common.type'),
      cell: row => {
        const types = []
        if (row.is_supplier) types.push(t('common.supplier'))
        if (row.is_customer) types.push(t('common.customer'))
        return types.length > 0 ? types.join(', ') : t('common.none')
      },
    },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('pages.business_parties.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getBusinessParties.data || []} columns={columns} />
    </div>
  )
}
