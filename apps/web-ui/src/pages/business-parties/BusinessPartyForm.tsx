import {
  <PERSON>ton,
  CheckboxField,
  Form,
  Form<PERSON><PERSON>,
  Header,
  InputField,
  RouteLink,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { type ComponentType, type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreateBusinessParty: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createBusinessParty = trpc.businessParties.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <BusinessPartyForm
      action={t('common.create')}
      title={t('business_parties.create_title')}
      values={{ name: '', is_supplier: false, is_customer: false }}
      onSubmit={async values => {
        await createBusinessParty.mutateAsync({
          ...values,
          company_id: companyId,
        })
      }}
    />
  )
}

export const EditBusinessParty: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const updateBusinessParty = trpc.businessParties.updateOne.useMutation()
  const getBusinessParty = trpc.businessParties.findOne.useQuery(
    { id: parseInt(id!) },
    { enabled: id !== undefined },
  )

  if (id === undefined || getBusinessParty.data === undefined) return null

  return (
    <BusinessPartyForm
      action={t('common.save')}
      title={t('business_parties.edit_title')}
      values={getBusinessParty.data}
      onSubmit={async values => {
        await updateBusinessParty.mutateAsync({ id: parseInt(id), ...values })
      }}
    />
  )
}

type BusinessParty = {
  name: string
  is_supplier: boolean
  is_customer: boolean
}
type BusinessPartyFormProps = {
  action: string
  title: string
  values: BusinessParty
  onSubmit: (values: BusinessParty) => Promise<void>
}
const BusinessPartyForm: ComponentType<BusinessPartyFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })
  const companyId = useCompanyId()

  if (companyId === null) {
    return <Typography>Please, select a company</Typography>
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.businessParties} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.businessParties,
            search: searchParams.toString(),
          })
        })}
      >
        <InputField
          control={form.control}
          name="name"
          type="text"
          label={t('common.name')}
        />
        <FormItem text={t('common.type')}>
          <div className="flex gap-2">
            <CheckboxField
              control={form.control}
              name="is_supplier"
              label={t('common.supplier')}
            />
            <CheckboxField
              control={form.control}
              name="is_customer"
              label={t('common.customer')}
            />
          </div>
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
