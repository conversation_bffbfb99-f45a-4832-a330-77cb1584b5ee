import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { BusinessParties } from './BusinessParties'
import { BusinessPartiesList } from './BusinessPartiesList'
import { CreateBusinessParty, EditBusinessParty } from './BusinessPartyForm'

export default {
  path: paths.businessParties,
  Component: BusinessParties,
  children: [
    { path: paths.new, Component: CreateBusinessParty },
    { path: paths.id, Component: EditBusinessParty },
    { path: '', Component: BusinessPartiesList },
  ],
} satisfies RouteObject
