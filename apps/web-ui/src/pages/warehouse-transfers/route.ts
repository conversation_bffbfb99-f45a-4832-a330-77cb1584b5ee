import { paths } from '@/constants'
import {
  Create<PERSON><PERSON>houseTransfer,
  EditWarehouseTransfer,
} from './WarehouseTransferForm'
import { WarehouseTransfers } from './WarehouseTransfers'
import { WarehouseTransfersList } from './WarehouseTransfersList'

export default {
  path: paths.warehouseTransfers,
  Component: WarehouseTransfers,
  children: [
    { path: paths.new, Component: CreateWarehouseTransfer },
    { path: paths.id, Component: EditWarehouseTransfer },
    { path: '', Component: WarehouseTransfersList },
  ],
}
