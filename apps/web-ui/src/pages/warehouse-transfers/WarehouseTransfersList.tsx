import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatNumber } from '@/utils'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type WarehouseTransfer = RouterOutputs['warehouseTransfers']['findMany'][number]

export const WarehouseTransfersList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const getWarehouseTransfers = trpc.warehouseTransfers.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<WarehouseTransfer>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    {
      title: t('table_columns.from_warehouse'),
      cell: row => row.from_warehouse.name,
    },
    {
      title: t('table_columns.to_warehouse'),
      cell: row => row.to_warehouse.name,
    },
    { title: t('table_columns.date'), cell: row => row.date },
    { title: t('table_columns.total'), cell: row => formatNumber(row.total) },
    { title: t('table_columns.items'), cell: row => row.items.length },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('warehouse_transfers.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getWarehouseTransfers.data || []} columns={columns} />
    </div>
  )
}
