import { ComboBoxField, ListTable, type Column } from '@/components'
import {
  <PERSON>ton,
  Form,
  FormItem,
  Header,
  InputField,
  NumberInputField,
  RouteLink,
  SelectField,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { cn, formatNumber, formatProductUnit } from '@/utils'
import { type ComponentType, type FC } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

type WarehouseTransferItem = {
  id?: number
  product_unit_id: number
  amount: number
  price: number
}

type WarehouseTransferFormData = {
  from_warehouse_id: number
  to_warehouse_id: number
  date: string
  items: Array<WarehouseTransferItem>
}

export const CreateWarehouseTransfer: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createWarehouseTransfer =
    trpc.warehouseTransfers.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <WarehouseTransferForm
      action={t('common.create')}
      title={t('warehouse_transfers.create_title')}
      values={{
        from_warehouse_id: 0,
        to_warehouse_id: 0,
        date: new Date().toISOString().split('T')[0],
        items: [{ product_unit_id: 0, amount: 1, price: 0 }],
      }}
      onSubmit={async values => {
        await createWarehouseTransfer.mutateAsync({
          company_id: companyId,
          from_warehouse_id: values.from_warehouse_id,
          to_warehouse_id: values.to_warehouse_id,
          date: values.date,
          items: values.items.map(item => ({
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          })),
        })
      }}
    />
  )
}

export const EditWarehouseTransfer: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const companyId = useCompanyId()
  const updateWarehouseTransfer =
    trpc.warehouseTransfers.updateOne.useMutation()
  const getWarehouseTransfer = trpc.warehouseTransfers.findOne.useQuery(
    { id: parseInt(id!), company_id: companyId! },
    { enabled: id !== undefined && companyId !== null },
  )

  if (id === undefined || companyId === null) return null
  if (getWarehouseTransfer.isLoading)
    return <Typography>{t('common.loading')}</Typography>
  if (getWarehouseTransfer.data === undefined)
    return <Typography>{t('common.warehouse_transfer_not_found')}</Typography>

  return (
    <WarehouseTransferForm
      action={t('common.save')}
      title={t('warehouse_transfers.edit_title')}
      values={{
        from_warehouse_id: getWarehouseTransfer.data.from_warehouse_id,
        to_warehouse_id: getWarehouseTransfer.data.to_warehouse_id,
        date: new Date(getWarehouseTransfer.data.date)
          .toISOString()
          .split('T')[0],
        items: getWarehouseTransfer.data.items.map(item => ({
          id: item.id,
          product_unit_id: item.product_unit_id,
          amount: item.amount,
          price: item.price,
        })),
      }}
      onSubmit={async values => {
        await updateWarehouseTransfer.mutateAsync({
          id: parseInt(id),
          from_warehouse_id: values.from_warehouse_id,
          to_warehouse_id: values.to_warehouse_id,
          date: values.date,
          items: values.items.map(item => ({
            id: item.id,
            product_unit_id: item.product_unit_id,
            amount: item.amount,
            price: item.price,
          })),
        })
      }}
    />
  )
}

type WarehouseTransferFormProps = {
  action: string
  title: string
  values: WarehouseTransferFormData
  onSubmit: (values: WarehouseTransferFormData) => Promise<void>
}

const WarehouseTransferForm: ComponentType<WarehouseTransferFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })
  const companyId = useCompanyId()

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  })

  const getWarehouses = trpc.warehouses.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const getProductUnits = trpc.products.findUnits.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const fromWarehouseId = form.watch('from_warehouse_id')
  const toWarehouseId = form.watch('to_warehouse_id')
  const warehousesAreSame =
    fromWarehouseId !== 0 &&
    toWarehouseId !== 0 &&
    fromWarehouseId === toWarehouseId

  const columns: Array<
    Column<WarehouseTransferItem & { index: number; id: number }>
  > = [
    {
      title: t('table_columns.product_unit'),
      cell: row => (
        <ComboBoxField
          className="w-full"
          control={form.control}
          name={`items.${row.index}.product_unit_id`}
          options={
            getProductUnits.data?.map(unit => ({
              value: String(unit.id),
              label: formatProductUnit(unit.product.name, unit),
            })) || []
          }
          onChange={() => {
            form.setFocus(`items.${row.index}.amount`)
          }}
        />
      ),
    },
    {
      title: t('table_columns.amount'),
      cell: ({ index }) => (
        <NumberInputField
          control={form.control}
          name={`items.${index}.amount`}
          decimalScale={3}
          fixedDecimalScale={false}
        />
      ),
    },
    {
      title: t('table_columns.price'),
      cell: ({ index }) => (
        <NumberInputField
          control={form.control}
          name={`items.${index}.price`}
          decimalScale={2}
          fixedDecimalScale={true}
        />
      ),
    },
    {
      title: t('table_columns.total'),
      cell: ({ index }) => {
        const item = form.watch(`items.${index}`)
        return (
          <span className="inline-block w-full border border-gray-300 px-2 py-1 text-right">
            {formatNumber(item.amount * item.price)}
          </span>
        )
      },
    },
    {
      title: t('table_columns.actions'),
      cell: ({ index }) => (
        <Button
          type="button"
          onClick={() => remove(index)}
          children={t('common.remove')}
        />
      ),
    },
  ]

  return (
    <div>
      <Header>{title}</Header>
      <nav>
        <RouteLink to={paths.warehouseTransfers} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Form
        onSubmit={form.handleSubmit(async data => {
          const fromWarehouseId = data.from_warehouse_id
          const toWarehouseId = data.to_warehouse_id

          if (fromWarehouseId === 0) {
            alert(t('forms.please_select_from_warehouse'))
            return
          }

          if (toWarehouseId === 0) {
            alert(t('forms.please_select_to_warehouse'))
            return
          }

          if (fromWarehouseId === toWarehouseId) {
            alert(t('forms.from_to_warehouses_same'))
            return
          }

          const validItems = data.items.filter(item => {
            const productUnitId = item.product_unit_id
            return productUnitId !== 0 && item.amount > 0 && item.price > 0
          })

          if (validItems.length === 0) {
            alert(t('forms.please_add_at_least_one_item'))
            return
          }

          await onSubmit({
            from_warehouse_id: fromWarehouseId,
            to_warehouse_id: toWarehouseId,
            date: data.date,
            items: validItems,
          })

          await navigate({
            pathname: paths.warehouseTransfers,
            search: searchParams.toString(),
          })
        })}
      >
        <div className="flex flex-col gap-2">
          <FormItem text={t('common.from_warehouse')}>
            <div>
              <SelectField
                control={form.control}
                name="from_warehouse_id"
                className={cn(
                  'w-full',
                  warehousesAreSame
                    ? 'border-red-500 bg-red-50'
                    : 'border-gray-300',
                )}
                options={[
                  { value: 0, label: t('forms.select_source_warehouse') },
                  ...(getWarehouses.data?.map(warehouse => ({
                    value: warehouse.id,
                    label: warehouse.name,
                  })) || []),
                ]}
              />
              {warehousesAreSame && (
                <Typography className="mt-1 text-sm text-red-600">
                  {t('forms.source_destination_different')}
                </Typography>
              )}
            </div>
          </FormItem>
          <FormItem text={t('common.to_warehouse')}>
            <div>
              <SelectField
                control={form.control}
                name="to_warehouse_id"
                className={cn(
                  'w-full',
                  warehousesAreSame
                    ? 'border-red-500 bg-red-50'
                    : 'border-gray-300',
                )}
                options={[
                  { value: 0, label: t('forms.select_destination_warehouse') },
                  ...(getWarehouses.data?.map(warehouse => ({
                    value: warehouse.id,
                    label: warehouse.name,
                  })) || []),
                ]}
              />
              {warehousesAreSame && (
                <Typography className="mt-1 text-sm text-red-600">
                  {t('forms.source_destination_different')}
                </Typography>
              )}
            </div>
          </FormItem>
          <InputField
            control={form.control}
            name="date"
            type="date"
            label={t('common.date')}
          />
          <div className="flex flex-col gap-2">
            <Typography>{t('warehouse_transfers.items_title')}</Typography>
            <ListTable
              columns={columns}
              rows={fields.map((field, index) => ({
                ...field,
                index,
                id: index,
              }))}
            />
            <div>
              <Button
                type="button"
                onClick={() =>
                  void append({ product_unit_id: 0, amount: 1, price: 0 })
                }
                children={t('common.add_item')}
              />
            </div>
          </div>
          <div className="text-right">
            <Typography weight="bold" className="flex justify-end gap-1">
              <span>{t('common.total')}:</span>
              <span>
                {formatNumber(
                  form
                    .watch('items')
                    .reduce((sum, item) => sum + item.amount * item.price, 0),
                )}
              </span>
            </Typography>
          </div>
          <Button
            type="submit"
            variant="primary"
            disabled={warehousesAreSame}
            children={action}
          />
        </div>
      </Form>
    </div>
  )
}
