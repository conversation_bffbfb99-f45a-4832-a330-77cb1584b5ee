import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatUnit } from '@/utils'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type Product = RouterOutputs['products']['findMany'][number]

const useColumns = (): Array<Column<Product>> => {
  const { t } = useTranslation()

  return [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.name'), cell: row => row.name },
    {
      title: t('common.units'),
      cell: row => (
        <div>
          {row.product_units.map((unit, index) => (
            <div key={unit.id}>
              {formatUnit(unit)}
              {index < row.product_units.length - 1 && <br />}
            </div>
          ))}
        </div>
      ),
    },
  ]
}

export const ProductsList: FC = () => {
  const companyId = useCompanyId()
  const { t } = useTranslation()
  const columns = useColumns()

  const getProducts = trpc.products.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('pages.products.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getProducts.data} columns={columns} />
    </div>
  )
}
