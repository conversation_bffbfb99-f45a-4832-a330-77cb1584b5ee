import { ListTable, RouteLinks } from '@/components'
import {
  <PERSON><PERSON>,
  <PERSON>,
  Header,
  InputField,
  NumberInputField,
  SelectField,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { cn } from '@/utils'
import { type ComponentType, type FC } from 'react'
import { useFieldArray, useForm, useWatch } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

type Unit = {
  id?: number | null
  parent_id?: number | null
  name: string
  multiplier: number
  barcode: string
}
type Product = { name: string; product_units: Array<Unit> }

export const CreateProduct: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createProduct = trpc.products.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <ProductForm
      action={t('common.create')}
      title={t('pages.products.title')}
      values={{
        name: '',
        product_units: [{ name: 'pc', multiplier: 1, barcode: '' }],
      }}
      onSubmit={async values => {
        await createProduct.mutateAsync({ ...values, company_id: companyId })
      }}
    />
  )
}

export const EditProduct: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const updateProduct = trpc.products.updateOne.useMutation()
  const getProduct = trpc.products.findOne.useQuery(
    { id: parseInt(id!) },
    { enabled: id !== undefined },
  )

  if (id === undefined || getProduct.data === undefined) return null

  return (
    <ProductForm
      action={t('common.save')}
      title={t('pages.products.title')}
      values={getProduct.data}
      productId={parseInt(id)}
      onSubmit={async values => {
        await updateProduct.mutateAsync({ id: parseInt(id), ...values })
      }}
    />
  )
}

type ProductFormProps = {
  action: string
  title: string
  values: Product
  onSubmit: (values: Product) => Promise<void>
  productId?: number
}

const ProductForm: ComponentType<ProductFormProps> = ({
  action,
  title,
  values,
  onSubmit,
  productId,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'product_units',
  })
  const units = useWatch({ control: form.control, name: 'product_units' })
  const companyId = useCompanyId()

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <RouteLinks
        links={[
          { to: paths.products, label: t('common.list') },
          {
            to: productId ? `${paths.productMovements}/${productId}` : '',
            label: t('common.productMovements'),
            hidden: productId === undefined,
          },
        ]}
      />
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.products,
            search: searchParams.toString(),
          })
        })}
      >
        <InputField
          control={form.control}
          name="name"
          type="text"
          label={t('common.name')}
        />
        <div className="flex flex-col gap-2">
          <Typography>{t('common.units')}</Typography>
          <ListTable
            columns={[
              {
                title: t('common.unit_name'),
                cell: row => (
                  <InputField
                    control={form.control}
                    name={`product_units.${row.index}.name`}
                    type="text"
                    className={cn('w-full', row.index === 0 && 'bg-gray-100')}
                    readOnly={row.index === 0}
                  />
                ),
              },
              {
                title: t('common.multiplier'),
                cell: row => (
                  <NumberInputField
                    control={form.control}
                    name={`product_units.${row.index}.multiplier`}
                    className={cn(
                      'w-full border border-gray-300 px-2 py-1 text-right',
                      row.index === 0 && 'bg-gray-100',
                    )}
                    decimalScale={0}
                    fixedDecimalScale={false}
                    allowNegative={false}
                    thousandSeparator={false}
                    readOnly={row.index === 0}
                  />
                ),
              },
              {
                title: t('common.parent_unit'),
                cell: row => (
                  <SelectField
                    control={form.control}
                    name={`product_units.${row.index}.parent_id`}
                    className="w-full"
                    options={[
                      { value: null, label: t('common.none') },
                      ...units
                        .map((unit, index) => ({ ...unit, index }))
                        .filter(unit => unit.index !== row.index)
                        .map(unit => ({
                          value: unit.id ?? null,
                          label: unit.name || `Unit #${unit.index + 1}`,
                          disabled: unit.id === undefined,
                        })),
                    ]}
                  />
                ),
              },
              {
                title: t('common.barcode'),
                cell: row => (
                  <InputField
                    control={form.control}
                    name={`product_units.${row.index}.barcode`}
                    type="number"
                    className="w-full"
                  />
                ),
              },
              {
                title: t('common.actions'),
                cell: row =>
                  fields.length > 1 &&
                  row.index > 0 && (
                    <Button
                      type="button"
                      onClick={() => {
                        remove(row.index)
                      }}
                      children={t('common.delete')}
                    />
                  ),
              },
            ]}
            rows={fields.map((field, index) => ({
              id: index,
              index,
              name: field.name || '',
              multiplier: field.multiplier || 1,
              barcode: field.barcode || '',
            }))}
          />
          <div>
            <Button
              type="button"
              onClick={() => {
                append({
                  name: '',
                  multiplier: 1,
                  barcode: '',
                  parent_id: null,
                })
              }}
              children={t('common.add') + ' ' + t('common.unit')}
            />
          </div>
        </div>
        <Button type="submit" variant="primary" children={action} />
      </Form>
    </div>
  )
}
