import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { CreateWarehouse, EditWarehouse } from './WarehouseForm'
import { Warehouses } from './Warehouses'
import { WarehousesList } from './WarehousesList'

export default {
  path: paths.warehouses,
  Component: Warehouses,
  children: [
    { path: paths.new, Component: CreateWarehouse },
    { path: paths.id, Component: EditWarehouse },
    { path: '', Component: WarehousesList },
  ],
} satisfies RouteObject
