import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type Warehouse = RouterOutputs['warehouses']['findMany'][number]

export const WarehousesList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const getWarehouses = trpc.warehouses.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<Warehouse>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('table_columns.name'), cell: row => row.name },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('datamatrix_responses.warehouses_title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getWarehouses.data} columns={columns} />
    </div>
  )
}
