import {
  <PERSON><PERSON>,
  <PERSON>,
  Header,
  <PERSON><PERSON><PERSON>ield,
  RouteLink,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { type ComponentType, type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreateWarehouse: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createWarehouse = trpc.warehouses.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <WarehouseForm
      action={t('common.create')}
      title={t('warehouses.create_title')}
      values={{ name: '' }}
      onSubmit={async values => {
        await createWarehouse.mutateAsync({ ...values, company_id: companyId })
      }}
    />
  )
}

export const EditWarehouse: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const updateWarehouse = trpc.warehouses.updateOne.useMutation()
  const getWarehouse = trpc.warehouses.findOne.useQuery(
    { id: parseInt(id!) },
    { enabled: id !== undefined },
  )

  if (id === undefined) return null
  if (getWarehouse.isLoading) return null
  if (getWarehouse.data === undefined || getWarehouse.data === null) return null

  return (
    <WarehouseForm
      action={t('common.save')}
      title={t('warehouses.edit_title')}
      values={{ name: getWarehouse.data.name }}
      onSubmit={async values => {
        await updateWarehouse.mutateAsync({ id: parseInt(id), ...values })
      }}
    />
  )
}

type Warehouse = { name: string }
type WarehouseFormProps = {
  action: string
  title: string
  values: Warehouse
  onSubmit: (values: Warehouse) => Promise<void>
}
const WarehouseForm: ComponentType<WarehouseFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })

  return (
    <div>
      <nav>
        <RouteLink to={paths.warehouses} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.warehouses,
            search: searchParams.toString(),
          })
        })}
      >
        <InputField
          control={form.control}
          name="name"
          type="text"
          label={t('common.name')}
        />
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
