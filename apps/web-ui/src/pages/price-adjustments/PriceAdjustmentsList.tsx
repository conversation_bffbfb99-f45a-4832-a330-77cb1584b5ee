import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type PriceAdjustment = RouterOutputs['priceAdjustments']['findMany'][number]

export const PriceAdjustmentsList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()

  const getPriceAdjustments = trpc.priceAdjustments.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<PriceAdjustment>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.date'), cell: row => row.date },
    { title: t('common.items_count'), cell: row => row.items.length },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('layout.sidebar.price_adjustments')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getPriceAdjustments.data} columns={columns} />
    </div>
  )
}
