import { ComboBox<PERSON>ield, ListTable, type Column } from '@/components'
import {
  <PERSON><PERSON>,
  Form,
  Header,
  InputField,
  NumberInputField,
  RouteLink,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatProductUnit } from '@/utils'
import { type ComponentType, type FC } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

type PriceAdjustmentItem = {
  id?: number
  product_unit_id: string | number
  price: number
}

type PriceAdjustmentFormData = {
  date: string
  items: Array<PriceAdjustmentItem>
}

export const CreatePriceAdjustment: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createPriceAdjustment = trpc.priceAdjustments.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <PriceAdjustmentForm
      action={t('common.create')}
      title={t('price_adjustments.create_title')}
      values={{
        date: new Date().toISOString().split('T')[0],
        items: [{ product_unit_id: '0', price: 0 }],
      }}
      onSubmit={async values => {
        await createPriceAdjustment.mutateAsync({
          ...values,
          company_id: companyId,
          items: values.items.map(item => ({
            product_unit_id: Number(item.product_unit_id),
            price: item.price,
          })),
        })
      }}
    />
  )
}

export const EditPriceAdjustment: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const companyId = useCompanyId()
  const updatePriceAdjustment = trpc.priceAdjustments.updateOne.useMutation()
  const getPriceAdjustment = trpc.priceAdjustments.findOne.useQuery(
    { id: parseInt(id!), company_id: companyId! },
    { enabled: id !== undefined && companyId !== null },
  )

  if (id === undefined || companyId === null) return null
  if (getPriceAdjustment.isLoading)
    return <Typography>{t('common.loading')}</Typography>
  if (getPriceAdjustment.data === undefined)
    return <Typography>{t('price_adjustments.not_found')}</Typography>

  const adjustment = getPriceAdjustment.data

  return (
    <PriceAdjustmentForm
      action={t('common.save')}
      title={t('price_adjustments.edit_title')}
      values={{
        date: adjustment.date,
        items: adjustment.items.map(item => ({
          id: item.id,
          product_unit_id: item.product_unit_id,
          price: item.price,
        })),
      }}
      onSubmit={async values => {
        await updatePriceAdjustment.mutateAsync({
          id: parseInt(id),
          ...values,
          items: values.items.map(item => ({
            id: item.id,
            product_unit_id: Number(item.product_unit_id),
            price: item.price,
          })),
        })
      }}
    />
  )
}

type PriceAdjustmentFormProps = {
  action: string
  title: string
  values: PriceAdjustmentFormData
  onSubmit: (values: PriceAdjustmentFormData) => Promise<void>
}

const PriceAdjustmentForm: ComponentType<PriceAdjustmentFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  const form = useForm<PriceAdjustmentFormData>({ values })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  })

  const getProductUnits = trpc.products.findUnits.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  return (
    <div>
      <Header>{title}</Header>
      <nav>
        <RouteLink to={paths.priceAdjustments} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Form
        onSubmit={form.handleSubmit(async data => {
          const validItems = data.items.filter(item => {
            const productUnitId = Number(item.product_unit_id)
            return (
              productUnitId !== 0 &&
              item.product_unit_id !== '0' &&
              item.price > 0
            )
          })
          if (validItems.length === 0) {
            alert(t('forms.please_add_at_least_one_item'))
            return
          }
          await onSubmit({ date: data.date, items: validItems })
          await navigate({
            pathname: paths.priceAdjustments,
            search: searchParams.toString(),
          })
        })}
      >
        <div className="flex flex-col gap-2">
          <InputField
            control={form.control}
            name="date"
            label={t('common.date')}
            type="date"
          />
          <div className="flex flex-col gap-2">
            <Typography>{t('common.items')}</Typography>
            <ListTable
              columns={
                [
                  {
                    title: t('common.product'),
                    cell: row => (
                      <ComboBoxField
                        className="w-full"
                        control={form.control}
                        name={`items.${row.index}.product_unit_id`}
                        options={
                          getProductUnits.data?.map(unit => ({
                            value: String(unit.id),
                            label: formatProductUnit(unit.product.name, unit),
                          })) || []
                        }
                        onChange={() => {
                          form.setFocus(`items.${row.index}.price`)
                        }}
                      />
                    ),
                  },
                  {
                    title: t('common.price'),
                    cell: row => (
                      <NumberInputField
                        control={form.control}
                        name={`items.${row.index}.price`}
                        decimalScale={2}
                        fixedDecimalScale={true}
                      />
                    ),
                  },
                  {
                    title: t('common.actions'),
                    cell: row => (
                      <Button
                        type="button"
                        onClick={() => void remove(row.index)}
                        children={t('common.delete')}
                      />
                    ),
                  },
                ] satisfies Array<Column<{ id: number; index: number }>>
              }
              rows={fields.map((_, index) => ({ id: index, index }))}
            />
            <div>
              <Button
                type="button"
                onClick={() => void append({ product_unit_id: '0', price: 0 })}
                children={t('common.add')}
              />
            </div>
          </div>
          <Button type="submit" variant="primary" children={action} />
        </div>
      </Form>
    </div>
  )
}
