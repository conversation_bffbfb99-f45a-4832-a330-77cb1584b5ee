import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatNumber } from '@/utils'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type SalesInvoice = RouterOutputs['salesInvoices']['findMany'][number]

export const SalesInvoicesList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const getSalesInvoices = trpc.salesInvoices.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<SalesInvoice>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.customer'), cell: row => row.customer.name },
    { title: t('common.warehouse'), cell: row => row.warehouse.name },
    { title: t('common.date'), cell: row => row.date },
    { title: t('common.total'), cell: row => formatNumber(row.total) },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('pages.sales_invoices.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getSalesInvoices.data || []} columns={columns} />
    </div>
  )
}
