import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { CreateSalesInvoice, EditSalesInvoice } from './SalesInvoiceForm'
import { SalesInvoices } from './SalesInvoices'
import { SalesInvoicesList } from './SalesInvoicesList'

export default {
  path: paths.salesInvoices,
  Component: SalesInvoices,
  children: [
    { path: paths.new, Component: CreateSalesInvoice },
    { path: paths.id, Component: EditSalesInvoice },
    { path: '', Component: SalesInvoicesList },
  ],
} satisfies RouteObject
