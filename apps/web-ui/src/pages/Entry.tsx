import { Button, List, ListItem } from '@/components/core'
import { useState, type FC } from 'react'
import { Login } from './Login'
import { Register } from './Register'

const pages = ['login', 'register'] as const

export const Entry: FC = () => {
  const [currentPage, setCurrentPage] =
    useState<(typeof pages)[number]>('login')

  return (
    <div className="mx-auto mt-8 max-w-sm p-2">
      <List>
        {pages.map(page => (
          <ListItem key={page}>
            <Button
              variant={page === currentPage ? 'primary' : 'default'}
              onClick={() => void setCurrentPage(page)}
            >
              {page}
            </Button>
          </ListItem>
        ))}
      </List>
      {currentPage === 'login' && <Login />}
      {currentPage === 'register' && <Register />}
    </div>
  )
}
