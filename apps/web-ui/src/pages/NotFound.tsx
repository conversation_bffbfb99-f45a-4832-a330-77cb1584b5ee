import { Link } from 'react-router'
import { paths } from '../constants'
import { useTranslation } from '../i18n'

export function NotFound() {
  const { t } = useTranslation()

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
      <div className="w-full max-w-lg space-y-8 text-center">
        <div>
          <h1 className="text-8xl font-bold text-gray-900 sm:text-9xl">404</h1>
          <h2 className="mt-6 text-2xl font-bold text-gray-900 sm:text-3xl">
            {t('not_found.title')}
          </h2>
          <p className="mt-2 text-sm text-gray-600 sm:text-base">
            {t('not_found.description')}
          </p>
        </div>
        <div className="space-y-3">
          <Link
            to={paths.root}
            className="flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-3 text-sm font-medium text-white shadow-sm transition-colors hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
          >
            {t('not_found.go_home')}
          </Link>
          <button
            onClick={() => window.history.back()}
            className="flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
          >
            {t('not_found.go_back')}
          </button>
        </div>
        <div className="rounded-lg bg-blue-50 p-4">
          <p className="text-sm text-blue-700">
            {t('not_found.contact_support')}
          </p>
        </div>
      </div>
    </div>
  )
}
