import { ComboBoxField, ListTable, type Column } from '@/components'
import {
  <PERSON><PERSON>,
  Form,
  FormItem,
  Header,
  InputField,
  NumberInputField,
  RouteLink,
  SelectField,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatNumber, formatProductUnit } from '@/utils'
import { type ComponentType, type FC } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

type PurchaseInvoiceItem = {
  id?: number
  product_unit_id: number
  amount: number
  price: number
}

type PurchaseInvoiceFormData = {
  supplier_id: number
  warehouse_id: number
  date: string
  items: Array<PurchaseInvoiceItem>
}

export const CreatePurchaseInvoice: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createPurchaseInvoice = trpc.purchaseInvoices.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <PurchaseInvoiceForm
      action={t('common.create')}
      title={t('pages.purchase_invoices.title')}
      values={{
        supplier_id: 0,
        warehouse_id: 0,
        date: new Date().toISOString().split('T')[0],
        items: [{ product_unit_id: 0, amount: 1, price: 0 }],
      }}
      onSubmit={async values => {
        await createPurchaseInvoice.mutateAsync({
          ...values,
          company_id: companyId,
        })
      }}
    />
  )
}

export const EditPurchaseInvoice: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const companyId = useCompanyId()
  const updatePurchaseInvoice = trpc.purchaseInvoices.updateOne.useMutation()
  const getPurchaseInvoice = trpc.purchaseInvoices.findOne.useQuery(
    { id: parseInt(id!), company_id: companyId! },
    { enabled: id !== undefined && companyId !== null },
  )

  if (id === undefined || companyId === null) return null
  if (getPurchaseInvoice.isLoading)
    return <Typography>{t('common.loading')}</Typography>
  if (getPurchaseInvoice.data === undefined)
    return <Typography>{t('purchase_invoices.not_found')}</Typography>

  return (
    <PurchaseInvoiceForm
      action={t('common.save')}
      title={t('pages.purchase_invoices.title')}
      values={{
        supplier_id: getPurchaseInvoice.data.supplier_id,
        warehouse_id: getPurchaseInvoice.data.warehouse_id,
        date: new Date(getPurchaseInvoice.data.date)
          .toISOString()
          .split('T')[0],
        items: getPurchaseInvoice.data.items.map(item => ({
          id: item.id,
          product_unit_id: item.product_unit_id,
          amount: item.amount,
          price: item.price,
        })),
      }}
      onSubmit={async values => {
        await updatePurchaseInvoice.mutateAsync({ id: parseInt(id), ...values })
      }}
    />
  )
}

type PurchaseInvoiceFormProps = {
  action: string
  title: string
  values: PurchaseInvoiceFormData
  onSubmit: (values: PurchaseInvoiceFormData) => Promise<void>
}

const PurchaseInvoiceForm: ComponentType<PurchaseInvoiceFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })
  const companyId = useCompanyId()

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  })

  const getSuppliers = trpc.businessParties.findMany.useQuery(
    { company_id: companyId!, is_supplier: true },
    { enabled: companyId !== null },
  )

  const getWarehouses = trpc.warehouses.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const getProductUnits = trpc.products.findUnits.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{title}</Header>
      <nav>
        <RouteLink to={paths.purchaseInvoices} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Form
        onSubmit={form.handleSubmit(async data => {
          if (data.supplier_id === 0) {
            alert(t('forms.please_select_supplier'))
            return
          }
          if (data.warehouse_id === 0) {
            alert(t('forms.please_select_warehouse'))
            return
          }
          const validItems = data.items.filter(
            item =>
              item.product_unit_id !== 0 && item.amount > 0 && item.price > 0,
          )
          if (validItems.length === 0) {
            alert(t('forms.please_add_at_least_one_item'))
            return
          }
          await onSubmit({ ...data, items: validItems })
          await navigate({
            pathname: paths.purchaseInvoices,
            search: searchParams.toString(),
          })
        })}
      >
        <div className="flex flex-col gap-2">
          <FormItem text={t('common.supplier')}>
            <SelectField
              control={form.control}
              name="supplier_id"
              className="border border-gray-300 px-2 py-1"
              options={[
                { value: 0, label: t('forms.select_supplier') },
                ...(getSuppliers.data?.map(supplier => ({
                  value: supplier.id,
                  label: supplier.name,
                })) || []),
              ]}
            />
          </FormItem>
          <FormItem text={t('common.warehouse')}>
            <SelectField
              control={form.control}
              name="warehouse_id"
              className="border border-gray-300 px-2 py-1"
              options={[
                { value: 0, label: t('forms.select_warehouse') },
                ...(getWarehouses.data?.map(warehouse => ({
                  value: warehouse.id,
                  label: warehouse.name,
                })) || []),
              ]}
            />
          </FormItem>
          <InputField
            control={form.control}
            name="date"
            type="date"
            label={t('common.date')}
          />
          <div className="flex flex-col gap-2">
            <Typography>{t('purchase_invoices.items_title')}</Typography>
            <ListTable
              columns={
                [
                  {
                    title: t('forms.product_unit'),
                    cell: row => (
                      <ComboBoxField
                        className="w-full"
                        control={form.control}
                        name={`items.${row.index}.product_unit_id`}
                        options={
                          getProductUnits.data?.map(unit => ({
                            value: String(unit.id),
                            label: formatProductUnit(unit.product.name, unit),
                          })) || []
                        }
                        onChange={() => {
                          form.setFocus(`items.${row.index}.amount`)
                        }}
                      />
                    ),
                  },
                  {
                    title: t('common.amount'),
                    cell: row => (
                      <NumberInputField
                        control={form.control}
                        name={`items.${row.index}.amount`}
                        decimalScale={3}
                        fixedDecimalScale={false}
                      />
                    ),
                  },
                  {
                    title: t('common.price'),
                    cell: row => (
                      <NumberInputField
                        control={form.control}
                        name={`items.${row.index}.price`}
                        decimalScale={2}
                        fixedDecimalScale={true}
                      />
                    ),
                  },
                  {
                    title: t('common.total'),
                    cell: row => {
                      const amount =
                        form.watch(`items.${row.index}.amount`) || 0
                      const price = form.watch(`items.${row.index}.price`) || 0
                      return (
                        <span className="inline-block w-full border border-gray-300 px-2 py-1 text-right">
                          {formatNumber(amount * price)}
                        </span>
                      )
                    },
                  },
                  {
                    title: t('common.actions'),
                    cell: row => (
                      <Button
                        type="button"
                        onClick={() => void remove(row.index)}
                        children={t('common.remove')}
                      />
                    ),
                  },
                ] satisfies Array<Column<{ id: number; index: number }>>
              }
              rows={fields.map((_, index) => ({ id: index, index }))}
            />
            <div>
              <Button
                type="button"
                onClick={() =>
                  void append({ product_unit_id: 0, amount: 1, price: 0 })
                }
                children={t('common.add_item')}
              />
            </div>
          </div>
          <div className="text-right">
            <Typography weight="bold" className="flex justify-end gap-1">
              <span>{t('common.total')}:</span>
              <span>
                {formatNumber(
                  form
                    .watch('items')
                    .reduce((sum, item) => sum + item.amount * item.price, 0),
                )}
              </span>
            </Typography>
          </div>
          <Button type="submit" variant="primary" children={action} />
        </div>
      </Form>
    </div>
  )
}
