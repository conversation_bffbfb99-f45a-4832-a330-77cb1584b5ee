import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import {
  CreatePurchaseInvoice,
  EditPurchaseInvoice,
} from './PurchaseInvoiceForm'
import { PurchaseInvoices } from './PurchaseInvoices'
import { PurchaseInvoicesList } from './PurchaseInvoicesList'

export default {
  path: paths.purchaseInvoices,
  Component: PurchaseInvoices,
  children: [
    { path: paths.new, Component: CreatePurchaseInvoice },
    { path: paths.id, Component: EditPurchaseInvoice },
    { path: '', Component: PurchaseInvoicesList },
  ],
} satisfies RouteObject
