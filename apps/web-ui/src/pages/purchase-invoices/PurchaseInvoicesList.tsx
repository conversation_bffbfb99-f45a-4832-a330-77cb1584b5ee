import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatNumber } from '@/utils'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type PurchaseInvoice = RouterOutputs['purchaseInvoices']['findMany'][number]

export const PurchaseInvoicesList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const getPurchaseInvoices = trpc.purchaseInvoices.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<PurchaseInvoice>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('table_columns.supplier'), cell: row => row.supplier.name },
    { title: t('table_columns.warehouse'), cell: row => row.warehouse.name },
    { title: t('table_columns.date'), cell: row => row.date },
    { title: t('table_columns.total'), cell: row => formatNumber(row.total) },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('datamatrix_responses.purchase_invoices_title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getPurchaseInvoices.data || []} columns={columns} />
    </div>
  )
}
