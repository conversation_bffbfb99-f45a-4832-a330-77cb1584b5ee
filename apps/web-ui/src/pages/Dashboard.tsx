import { Layout } from '@/components'
import { restoreToken } from '@/redux/slices/authSlice'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { useEffect, type FC } from 'react'
import { shallowEqual } from 'react-redux'
import { Outlet } from 'react-router'
import * as remeda from 'remeda'
import { Entry } from './Entry'

export const Dashboard: FC = () => {
  const { isRestoreTriggered, isRestoring, isAuthenticated } = useAppSelector(
    state =>
      remeda.pick(state.auth, [
        'isRestoreTriggered',
        'isRestoring',
        'isAuthenticated',
      ]),
    shallowEqual,
  )
  const dispatch = useAppDispatch()

  useEffect(() => {
    void dispatch(restoreToken())
  }, [dispatch])

  if (!isRestoreTriggered || isRestoring) return null

  if (!isAuthenticated) {
    return <Entry />
  }

  return (
    <Layout>
      <Outlet />
    </Layout>
  )
}
