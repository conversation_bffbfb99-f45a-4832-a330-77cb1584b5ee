import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type Price = RouterOutputs['prices']['findMany'][number]

export const PricesList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()

  const getPrices = trpc.prices.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<Price>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.name'), cell: row => row.name },
    { title: t('common.type'), cell: row => t(`prices.type_${row.type}`) },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('pages.prices.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getPrices.data} columns={columns} />
    </div>
  )
}
