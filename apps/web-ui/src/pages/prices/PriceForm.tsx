import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Header,
  InputField,
  RouteLink,
  SelectField,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { type ComponentType, type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreatePrice: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createPrice = trpc.prices.createOne.useMutation()

  if (companyId === null) {
    return <div>{t('messages.select_company')}</div>
  }

  return (
    <PriceForm
      action={t('common.create')}
      title={t('prices.create_title')}
      values={{ name: '', type: 'purchase' }}
      onSubmit={async values => {
        await createPrice.mutateAsync({ ...values, company_id: companyId })
      }}
    />
  )
}

export const EditPrice: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const updatePrice = trpc.prices.updateOne.useMutation()
  const getPrice = trpc.prices.findOne.useQuery(
    { id: parseInt(id!) },
    { enabled: id !== undefined },
  )

  if (id === undefined) return null
  if (getPrice.isLoading) return null
  if (getPrice.data === undefined) return null

  return (
    <PriceForm
      action={t('common.save')}
      title={t('prices.edit_title')}
      values={{ name: getPrice.data.name, type: getPrice.data.type }}
      onSubmit={async values => {
        await updatePrice.mutateAsync({ id: parseInt(id), ...values })
      }}
    />
  )
}

type Price = { name: string; type: 'purchase' | 'sales' }
type PriceFormProps = {
  action: string
  title: string
  values: Price
  onSubmit: (values: Price) => Promise<void>
}

const PriceForm: ComponentType<PriceFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })

  const typeOptions = [
    { value: 'purchase', label: t('prices.type_purchase') },
    { value: 'sales', label: t('prices.type_sales') },
  ]

  return (
    <div>
      <nav>
        <RouteLink to={paths.prices} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.prices,
            search: searchParams.toString(),
          })
        })}
      >
        <InputField
          control={form.control}
          name="name"
          type="text"
          label={t('common.name')}
        />
        <FormItem text={t('common.type')}>
          <SelectField
            control={form.control}
            name="type"
            options={typeOptions}
          />
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
