import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, RouteLink, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type AuthorizedUser = RouterOutputs['users']['findAuthorized'][number]

export const AuthorizedUsersList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()

  const getAuthorizedUsers = trpc.users.findAuthorized.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<AuthorizedUser>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    {
      title: t('table_columns.email'),
      cell: row => <RouteLink to={row.id.toString()}>{row.email}</RouteLink>,
    },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('datamatrix_responses.authorized_users_title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable columns={columns} rows={getAuthorizedUsers.data} />
    </div>
  )
}
