import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  RouteLink,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useSearchParams } from 'react-router'

export const AuthorizeUserForm: FC = () => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values: { email: '' } })
  const companyId = useCompanyId()
  const authorizeUser = trpc.users.authorize.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.authorizedUsers} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Header>{t('datamatrix_responses.authorize_user_title')}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await authorizeUser.mutateAsync({ ...values, company_id: companyId })
          await navigate({
            pathname: paths.authorizedUsers,
            search: searchParams.toString(),
          })
        })}
      >
        <InputField
          control={form.control}
          name="email"
          type="email"
          label={t('common.email')}
        />
        <Button type="submit" variant="primary">
          {t('common.authorize')}
        </Button>
      </Form>
    </div>
  )
}
