import { Identity, ListTable, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatNumber } from '@/utils'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'
import { useParams } from 'react-router'

type ProductMovement = RouterOutputs['productMovements']['findMany'][number]

export const ProductMovements: FC = () => {
  const { t } = useTranslation()
  const { productId } = useParams()
  const companyId = useCompanyId()

  const columns: Array<Column<ProductMovement>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.date'), cell: row => row.date },
    { title: t('common.warehouse'), cell: row => row.warehouse.name },
    { title: t('common.unit'), cell: row => row.product_unit.name },
    { title: t('common.quantity'), cell: row => row.quantity },
    { title: t('common.price'), cell: row => formatNumber(row.price) },
    {
      title: t('common.type'),
      cell: row => {
        if (row.product_inventory_item_id)
          return t('layout.sidebar.product_inventories')
        if (row.purchase_invoice_item_id)
          return t('layout.sidebar.purchase_invoices')
        if (row.sales_invoice_item_id) return t('layout.sidebar.sales_invoices')
        if (row.warehouse_transfer_item_id)
          return t('warehouse_transfers.title')
        return t('common.unknown_error')
      },
    },
  ]

  const getProduct = trpc.products.findOne.useQuery(
    { id: parseInt(productId!) },
    { enabled: productId !== undefined },
  )

  const getProductMovements = trpc.productMovements.findMany.useQuery(
    { product_id: parseInt(productId!), company_id: companyId! },
    { enabled: productId !== undefined && companyId !== null },
  )

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  if (productId === undefined) {
    return <Typography>{t('messages.product_id_required')}</Typography>
  }

  return (
    <div>
      <Header>{t('common.productMovements')}</Header>
      {getProduct.data && (
        <Typography className="flex gap-1">
          <span>{t('common.product')}:</span>
          <span>{getProduct.data.name}</span>
        </Typography>
      )}
      <ListTable rows={getProductMovements.data || []} columns={columns} />
    </div>
  )
}
