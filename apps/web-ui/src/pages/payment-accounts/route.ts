import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import { CreatePaymentAccount, EditPaymentAccount } from './PaymentAccountForm'
import { PaymentAccounts } from './PaymentAccounts'
import { PaymentAccountsList } from './PaymentAccountsList'

export default {
  path: paths.paymentAccounts,
  Component: PaymentAccounts,
  children: [
    { path: paths.new, Component: CreatePaymentAccount },
    { path: paths.id, Component: EditPaymentAccount },
    { path: '', Component: PaymentAccountsList },
  ],
} satisfies RouteObject
