import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  InputField,
  RouteLink,
  SelectField,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { type ComponentType, type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreatePaymentAccount: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createPaymentAccount = trpc.paymentAccounts.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <PaymentAccountForm
      action={t('common.create')}
      title={t('payment_accounts.create_title')}
      values={{ name: '', type: 'bank' }}
      onSubmit={async values => {
        await createPaymentAccount.mutateAsync({
          ...values,
          company_id: companyId,
        })
      }}
    />
  )
}

export const EditPaymentAccount: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const companyId = useCompanyId()
  const updatePaymentAccount = trpc.paymentAccounts.updateOne.useMutation()
  const getPaymentAccount = trpc.paymentAccounts.findOne.useQuery(
    { id: parseInt(id!), company_id: companyId! },
    { enabled: id !== undefined && companyId !== null },
  )

  if (id === undefined) return null
  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }
  if (getPaymentAccount.isLoading) return null
  if (!getPaymentAccount.data) return null

  return (
    <PaymentAccountForm
      action={t('common.update')}
      title={t('payment_accounts.edit_title')}
      values={{
        name: getPaymentAccount.data.name,
        type: getPaymentAccount.data.type,
      }}
      onSubmit={async values => {
        await updatePaymentAccount.mutateAsync({
          ...values,
          id: parseInt(id),
          company_id: companyId,
        })
      }}
    />
  )
}

type PaymentAccount = { name: string; type: 'bank' | 'cash' }
type PaymentAccountFormProps = {
  action: string
  title: string
  values: PaymentAccount
  onSubmit: (values: PaymentAccount) => Promise<void>
}
const PaymentAccountForm: ComponentType<PaymentAccountFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })

  return (
    <div>
      <nav>
        <RouteLink to={paths.paymentAccounts} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          void navigate({
            pathname: paths.paymentAccounts,
            search: searchParams.toString(),
          })
        })}
      >
        <InputField
          control={form.control}
          name="name"
          type="text"
          label={t('common.name')}
        />
        <FormItem text={t('common.type')}>
          <SelectField
            control={form.control}
            name="type"
            options={[
              { value: 'bank', label: t('payment_accounts.type_bank') },
              { value: 'cash', label: t('payment_accounts.type_cash') },
            ]}
          />
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
