import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatNumber } from '@/utils'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type BusinessPartyInventory =
  RouterOutputs['businessPartyInventories']['findMany'][number]

const useColumns = (): Array<Column<BusinessPartyInventory>> => {
  const { t } = useTranslation()

  return [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.business_party'), cell: row => row.business_party.name },
    {
      title: t('common.amount'),
      cell: row => formatNumber(Number(row.amount)),
    },
  ]
}

export const BusinessPartyInventoriesList: FC = () => {
  const companyId = useCompanyId()
  const { t } = useTranslation()
  const columns = useColumns()
  const getBusinessPartyInventories =
    trpc.businessPartyInventories.findMany.useQuery(
      { company_id: companyId! },
      { enabled: companyId !== null },
    )

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('pages.business_party_inventories.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable
        rows={getBusinessPartyInventories.data || []}
        columns={columns}
      />
    </div>
  )
}
