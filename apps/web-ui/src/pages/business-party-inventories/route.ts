import { paths } from '@/constants'
import type { RouteObject } from 'react-router'
import { BusinessPartyInventories } from './BusinessPartyInventories'
import { BusinessPartyInventoriesList } from './BusinessPartyInventoriesList'
import {
  CreateBusinessPartyInventory,
  EditBusinessPartyInventory,
} from './BusinessPartyInventoryForm'

export default {
  path: paths.businessPartyInventories,
  Component: BusinessPartyInventories,
  children: [
    { path: '', Component: BusinessPartyInventoriesList },
    { path: paths.new, Component: CreateBusinessPartyInventory },
    { path: paths.id, Component: EditBusinessPartyInventory },
  ],
} satisfies RouteObject
