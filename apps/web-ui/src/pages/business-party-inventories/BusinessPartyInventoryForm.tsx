import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>I<PERSON>,
  Header,
  Input,
  NumberInputField,
  RouteLink,
  SelectField,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { type ComponentType, type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreateBusinessPartyInventory: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createBusinessPartyInventory =
    trpc.businessPartyInventories.createOne.useMutation()
  const getBusinessParties = trpc.businessParties.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <BusinessPartyInventoryForm
      action={t('common.create')}
      title={t('business_party_inventories.create_title')}
      values={{ business_party_id: '', amount: 0 }}
      businessParties={getBusinessParties.data || []}
      onSubmit={async values => {
        await createBusinessPartyInventory.mutateAsync({
          ...values,
          business_party_id: parseInt(values.business_party_id),
          company_id: companyId,
        })
      }}
    />
  )
}

export const EditBusinessPartyInventory: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const companyId = useCompanyId()
  const updateBusinessPartyInventory =
    trpc.businessPartyInventories.updateOne.useMutation()
  const getBusinessPartyInventory =
    trpc.businessPartyInventories.findOne.useQuery(
      { id: parseInt(id!) },
      { enabled: id !== undefined },
    )
  const getBusinessParties = trpc.businessParties.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  if (id === undefined || getBusinessPartyInventory.data === undefined)
    return null

  return (
    <BusinessPartyInventoryForm
      action={t('common.save')}
      title={t('business_party_inventories.edit_title')}
      values={{
        business_party_id: String(
          getBusinessPartyInventory.data.business_party_id,
        ),
        amount: Number(getBusinessPartyInventory.data.amount),
      }}
      businessParties={getBusinessParties.data || []}
      onSubmit={async values => {
        await updateBusinessPartyInventory.mutateAsync({
          id: parseInt(id),
          amount: parseFloat(values.amount.toString()),
        })
      }}
      isEdit
    />
  )
}

type BusinessPartyInventory = { business_party_id: string; amount: number }

type BusinessParty = { id: number; name: string }

type BusinessPartyInventoryFormProps = {
  action: string
  title: string
  values: BusinessPartyInventory
  businessParties: Array<BusinessParty>
  onSubmit: (values: BusinessPartyInventory) => Promise<void>
  isEdit?: boolean
}

const BusinessPartyInventoryForm: ComponentType<
  BusinessPartyInventoryFormProps
> = ({ action, title, values, businessParties, onSubmit, isEdit = false }) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })
  const companyId = useCompanyId()

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.businessPartyInventories} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.businessPartyInventories,
            search: searchParams.toString(),
          })
        })}
      >
        <FormItem text={t('common.business_party')}>
          {isEdit ? (
            <Input
              type="text"
              value={
                businessParties.find(
                  bp => bp.id === parseInt(values.business_party_id),
                )?.name || ''
              }
              disabled
            />
          ) : (
            <SelectField
              className="w-full"
              control={form.control}
              name="business_party_id"
              options={[
                { value: '', label: t('forms.select_business_party') },
                ...businessParties.map(businessParty => ({
                  value: String(businessParty.id),
                  label: businessParty.name,
                })),
              ]}
            />
          )}
        </FormItem>
        <FormItem text={t('common.amount')}>
          <NumberInputField
            control={form.control}
            name="amount"
            allowNegative={true}
          />
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
