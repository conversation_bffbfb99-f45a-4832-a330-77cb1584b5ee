import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatNumber } from '@/utils'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type ProductInventory = RouterOutputs['productInventories']['findMany'][number]

const useColumns = (): Array<Column<ProductInventory>> => {
  const { t } = useTranslation()

  return [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.warehouse'), cell: row => row.warehouse.name },
    { title: t('common.date'), cell: row => row.date },
    { title: t('common.total'), cell: row => formatNumber(row.total) },
  ]
}

export const ProductInventoriesList: FC = () => {
  const companyId = useCompanyId()
  const { t } = useTranslation()
  const columns = useColumns()
  const getProductInventories = trpc.productInventories.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('pages.product_inventories.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getProductInventories.data || []} columns={columns} />
    </div>
  )
}
