import { ComboBoxField, ListTable, type Column } from '@/components'
import {
  <PERSON>ton,
  Form,
  FormItem,
  Header,
  InputField,
  NumberInputField,
  RouteLink,
  SelectField,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatNumber, formatProductUnit } from '@/utils'
import { type ComponentType, type FC } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

type ProductInventoryItem = {
  id?: number
  product_unit_id: string | number
  amount: number
  price: number
}

type ProductInventoryFormData = {
  warehouse_id: string | number
  date: string
  items: Array<ProductInventoryItem>
}

export const CreateProductInventory: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createProductInventory = trpc.productInventories.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <ProductInventoryForm
      action={t('common.create')}
      title={t('product_inventories.create_title')}
      values={{
        warehouse_id: '0',
        date: new Date().toISOString().split('T')[0],
        items: [{ product_unit_id: '0', amount: 1, price: 0 }],
      }}
      onSubmit={async values => {
        await createProductInventory.mutateAsync({
          company_id: companyId,
          warehouse_id: Number(values.warehouse_id),
          date: values.date,
          items: values.items.map(item => ({
            product_unit_id: Number(item.product_unit_id),
            amount: item.amount,
            price: item.price,
          })),
        })
      }}
    />
  )
}

export const EditProductInventory: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const companyId = useCompanyId()
  const updateProductInventory = trpc.productInventories.updateOne.useMutation()
  const getProductInventory = trpc.productInventories.findOne.useQuery(
    { id: parseInt(id!), company_id: companyId! },
    { enabled: id !== undefined && companyId !== null },
  )

  if (id === undefined || companyId === null) return null
  if (getProductInventory.isLoading)
    return <Typography>{t('common.loading')}</Typography>
  if (getProductInventory.data === undefined)
    return <Typography>{t('product_inventories.not_found')}</Typography>

  return (
    <ProductInventoryForm
      action={t('common.save')}
      title={t('product_inventories.edit_title')}
      values={{
        warehouse_id: getProductInventory.data.warehouse_id,
        date: new Date(getProductInventory.data.date)
          .toISOString()
          .split('T')[0],
        items: getProductInventory.data.items.map(item => ({
          id: item.id,
          product_unit_id: item.product_unit_id,
          amount: item.amount,
          price: item.price,
        })),
      }}
      onSubmit={async values => {
        await updateProductInventory.mutateAsync({
          id: parseInt(id),
          warehouse_id: Number(values.warehouse_id),
          date: values.date,
          items: values.items.map(item => ({
            id: item.id,
            product_unit_id: Number(item.product_unit_id),
            amount: item.amount,
            price: item.price,
          })),
        })
      }}
    />
  )
}

type ProductInventoryFormProps = {
  action: string
  title: string
  values: ProductInventoryFormData
  onSubmit: (values: ProductInventoryFormData) => Promise<void>
}

const ProductInventoryForm: ComponentType<ProductInventoryFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })
  const companyId = useCompanyId()

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  })

  const getWarehouses = trpc.warehouses.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const getProductUnits = trpc.products.findUnits.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const itemsTotal = form
    .watch('items')
    .reduce((sum, item) => sum + item.amount * item.price, 0)

  return (
    <div>
      <Header>{title}</Header>
      <nav>
        <RouteLink to={paths.productInventories} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Form
        onSubmit={form.handleSubmit(async data => {
          const warehouseId = Number(data.warehouse_id)
          if (warehouseId === 0 || data.warehouse_id === '0') {
            alert(t('forms.please_select_warehouse'))
            return
          }
          const validItems = data.items.filter(item => {
            const productUnitId = Number(item.product_unit_id)
            return (
              productUnitId !== 0 &&
              item.product_unit_id !== '0' &&
              item.amount > 0 &&
              item.price > 0
            )
          })
          if (validItems.length === 0) {
            alert(t('forms.please_add_at_least_one_item'))
            return
          }
          await onSubmit({
            warehouse_id: warehouseId,
            date: data.date,
            items: validItems,
          })
          await navigate({
            pathname: paths.productInventories,
            search: searchParams.toString(),
          })
        })}
      >
        <div className="flex flex-col gap-2">
          <FormItem text={t('common.warehouse')}>
            <SelectField
              control={form.control}
              name="warehouse_id"
              options={[
                { value: '0', label: t('forms.select_warehouse') },
                ...(getWarehouses.data?.map(warehouse => ({
                  value: String(warehouse.id),
                  label: warehouse.name,
                })) || []),
              ]}
            />
          </FormItem>
          <InputField
            control={form.control}
            name="date"
            type="date"
            label={t('common.date')}
          />
          <div className="flex flex-col gap-2">
            <Typography>{t('product_inventories.items_title')}</Typography>
            <ListTable
              columns={
                [
                  {
                    title: t('forms.product_unit'),
                    cell: row => (
                      <ComboBoxField
                        className="w-full"
                        control={form.control}
                        name={`items.${row.index}.product_unit_id`}
                        options={
                          getProductUnits.data?.map(unit => ({
                            value: String(unit.id),
                            label: formatProductUnit(unit.product.name, unit),
                          })) || []
                        }
                        onChange={() => {
                          form.setFocus(`items.${row.index}.amount`)
                        }}
                      />
                    ),
                  },
                  {
                    title: t('common.amount'),
                    cell: row => (
                      <NumberInputField
                        control={form.control}
                        name={`items.${row.index}.amount`}
                        decimalScale={3}
                        fixedDecimalScale={false}
                      />
                    ),
                  },
                  {
                    title: t('common.price'),
                    cell: row => (
                      <NumberInputField
                        control={form.control}
                        name={`items.${row.index}.price`}
                        decimalScale={2}
                        fixedDecimalScale={true}
                      />
                    ),
                  },
                  {
                    title: t('common.total'),
                    cell: row => {
                      const amount =
                        form.watch(`items.${row.index}.amount`) || 0
                      const price = form.watch(`items.${row.index}.price`) || 0
                      return (
                        <span className="inline-block w-full border border-gray-300 px-2 py-1 text-right">
                          {formatNumber(amount * price)}
                        </span>
                      )
                    },
                  },
                  {
                    title: t('common.actions'),
                    cell: row => (
                      <Button
                        type="button"
                        onClick={() => void remove(row.index)}
                        children={t('common.delete')}
                      />
                    ),
                  },
                ] satisfies Array<Column<{ id: number; index: number }>>
              }
              rows={fields.map((field, index) => ({ id: index, index }))}
            />
            <div>
              <Button
                type="button"
                onClick={() =>
                  void append({ product_unit_id: '0', amount: 1, price: 0 })
                }
                children={t('common.add')}
              />
            </div>
          </div>
          <div className="text-right">
            <Typography weight="bold" className="flex justify-end gap-1">
              <span>{t('common.total')}:</span>
              <span>{formatNumber(itemsTotal)}</span>
            </Typography>
          </div>
          <Button type="submit" variant="primary" children={action} />
        </div>
      </Form>
    </div>
  )
}
