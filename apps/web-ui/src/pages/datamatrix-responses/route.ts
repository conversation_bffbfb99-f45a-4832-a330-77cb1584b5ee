import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import {
  CreateDatamatrixResponse,
  EditDatamatrixResponse,
} from './DatamatrixResponseForm'
import { DatamatrixResponses } from './DatamatrixResponses'
import { DatamatrixResponsesList } from './DatamatrixResponsesList'

export default {
  path: paths.datamatrixResponses,
  Component: DatamatrixResponses,
  children: [
    { path: paths.new, Component: CreateDatamatrixResponse },
    { path: paths.id, Component: EditDatamatrixResponse },
    { path: '', Component: DatamatrixResponsesList },
  ],
} satisfies RouteObject
