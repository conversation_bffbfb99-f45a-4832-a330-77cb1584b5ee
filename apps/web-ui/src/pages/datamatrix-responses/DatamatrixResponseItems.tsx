import {
  <PERSON><PERSON>ell,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ell,
  Header,
  Input,
  RouteLink,
  Select,
  Table,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatProductUnit } from '@/utils'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC, useState } from 'react'
import { useParams } from 'react-router'

type DatamatrixResponseItem =
  RouterOutputs['datamatrixResponses']['findItems'][number]

export const DatamatrixResponseItems: FC = () => {
  const { t } = useTranslation()
  const { id: datamatrixResponseId } = useParams()
  const companyId = useCompanyId()
  const utils = trpc.useUtils()
  const [editingItem, setEditingItem] = useState<DatamatrixResponseItem | null>(
    null,
  )
  const [newItem, setNewItem] = useState({ product_unit_id: 0, datamatrix: '' })

  const getDatamatrixResponse = trpc.datamatrixResponses.findOne.useQuery(
    { id: parseInt(datamatrixResponseId!) },
    { enabled: datamatrixResponseId !== undefined },
  )
  const getDatamatrixResponseItems =
    trpc.datamatrixResponses.findItems.useQuery(
      { datamatrix_response_id: parseInt(datamatrixResponseId!) },
      { enabled: datamatrixResponseId !== undefined },
    )
  const getProducts = trpc.products.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const updateDatamatrixResponse =
    trpc.datamatrixResponses.updateOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  if (
    datamatrixResponseId === undefined ||
    getDatamatrixResponse.data === undefined
  ) {
    return <Typography>{t('common.loading')}</Typography>
  }

  const items = getDatamatrixResponseItems.data || []

  const handleAddItem = async () => {
    if (newItem.product_unit_id === 0 || newItem.datamatrix.trim() === '')
      return

    const updatedItems = [...items, newItem]
    await updateDatamatrixResponse.mutateAsync({
      id: parseInt(datamatrixResponseId),
      items: updatedItems,
    })

    await utils.datamatrixResponses.findOne.invalidate({
      id: parseInt(datamatrixResponseId),
    })
    await utils.datamatrixResponses.findItems.invalidate({
      datamatrix_response_id: parseInt(datamatrixResponseId),
    })
    setNewItem({ product_unit_id: 0, datamatrix: '' })
  }

  const handleSaveEdit = async () => {
    if (editingItem === null) return

    const updatedItems = items.map(item =>
      item.id === editingItem.id ? editingItem : item,
    )

    await updateDatamatrixResponse.mutateAsync({
      id: parseInt(datamatrixResponseId),
      items: updatedItems,
    })

    await utils.datamatrixResponses.findOne.invalidate({
      id: parseInt(datamatrixResponseId),
    })
    await utils.datamatrixResponses.findItems.invalidate({
      datamatrix_response_id: parseInt(datamatrixResponseId),
    })
    setEditingItem(null)
  }

  const handleDeleteItem = async (itemId: number) => {
    const updatedItems = items.filter(item => item.id !== itemId)
    await updateDatamatrixResponse.mutateAsync({
      id: parseInt(datamatrixResponseId),
      items: updatedItems,
    })

    await utils.datamatrixResponses.findOne.invalidate({
      id: parseInt(datamatrixResponseId),
    })
    await utils.datamatrixResponses.findItems.invalidate({
      datamatrix_response_id: parseInt(datamatrixResponseId),
    })
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.datamatrixResponses} end>
          {t('datamatrix_responses.back_to_datamatrix_responses')}
        </RouteLink>
      </nav>
      <Header>
        {t('datamatrix_responses.items_header', { id: datamatrixResponseId })}
      </Header>
      <div>
        <Typography>{t('datamatrix_responses.add_new_item')}</Typography>
        <div>
          <FormItem text={t('forms.product_unit')}>
            <Select
              value={newItem.product_unit_id.toString()}
              onChange={event => {
                setNewItem({
                  ...newItem,
                  product_unit_id: parseInt(event.target.value) || 0,
                })
              }}
            >
              <option value="0">{t('forms.select_product_unit')}</option>
              {getProducts.data?.flatMap(product =>
                product.product_units.map(unit => (
                  <option key={unit.id} value={unit.id}>
                    {formatProductUnit(product.name, unit)}
                  </option>
                )),
              )}
            </Select>
          </FormItem>
          <FormItem text={t('common.datamatrix')}>
            <Input
              type="text"
              value={newItem.datamatrix}
              onChange={event => {
                setNewItem({ ...newItem, datamatrix: event.target.value })
              }}
              placeholder={t('forms.enter_datamatrix_code')}
            />
          </FormItem>
          <Button type="button" onClick={handleAddItem}>
            Add Item
          </Button>
        </div>
      </div>
      <div>
        <Typography>{t('datamatrix_responses.current_items')}</Typography>
        <Table>
          <thead>
            <tr>
              <HeadCell>{t('table_columns.product')}</HeadCell>
              <HeadCell>{t('table_columns.unit')}</HeadCell>
              <HeadCell>{t('table_columns.barcode')}</HeadCell>
              <HeadCell>{t('table_columns.datamatrix')}</HeadCell>
              <HeadCell>{t('table_columns.actions')}</HeadCell>
            </tr>
          </thead>
          <tbody>
            {items.map(item => (
              <tr key={item.id}>
                <BodyCell>{item.product_unit.product.name}</BodyCell>
                <BodyCell>{item.product_unit.name}</BodyCell>
                <BodyCell>{item.product_unit.barcode}</BodyCell>
                <BodyCell>
                  {editingItem?.id === item.id ? (
                    <Input
                      value={editingItem.datamatrix || ''}
                      onChange={event => {
                        setEditingItem({
                          ...editingItem,
                          datamatrix: event.target.value,
                        })
                      }}
                    />
                  ) : (
                    item.datamatrix
                  )}
                </BodyCell>
                <BodyCell>
                  {editingItem?.id === item.id ? (
                    <div className="flex gap-2">
                      <Button type="button" onClick={handleSaveEdit}>
                        Save
                      </Button>
                      <Button
                        type="button"
                        onClick={() => setEditingItem(null)}
                      >
                        {t('common.cancel')}
                      </Button>
                    </div>
                  ) : (
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        onClick={() => setEditingItem(item)}
                      >
                        Edit
                      </Button>
                      <Button
                        type="button"
                        onClick={() => handleDeleteItem(item.id)}
                      >
                        Delete
                      </Button>
                    </div>
                  )}
                </BodyCell>
              </tr>
            ))}
          </tbody>
        </Table>
      </div>
    </div>
  )
}
