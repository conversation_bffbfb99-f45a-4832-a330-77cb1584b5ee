import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type DatamatrixResponse =
  RouterOutputs['datamatrixResponses']['findMany'][number]

export const DatamatrixResponsesList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const getDatamatrixResponses = trpc.datamatrixResponses.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<DatamatrixResponse>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('pages.datamatrix_responses.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getDatamatrixResponses.data || []} columns={columns} />
    </div>
  )
}
