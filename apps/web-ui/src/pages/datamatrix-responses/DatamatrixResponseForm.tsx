import { ComboBoxField } from '@/components'
import {
  BodyCell,
  Button,
  Form,
  HeadCell,
  Header,
  Input,
  RouteLink,
  Table,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatProductUnit } from '@/utils'
import { type ComponentType, type FC } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

type DatamatrixResponseItem = {
  id?: number
  product_unit_id: number
  datamatrix: string
}

type DatamatrixResponseFormData = { items: Array<DatamatrixResponseItem> }

export const CreateDatamatrixResponse: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createDatamatrixResponse =
    trpc.datamatrixResponses.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <DatamatrixResponseForm
      action={t('common.create')}
      title={t('datamatrix_responses.create_title')}
      onSubmit={async data => {
        await createDatamatrixResponse.mutateAsync({
          company_id: companyId,
          items: data.items,
        })
      }}
    />
  )
}

export const EditDatamatrixResponse: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const utils = trpc.useUtils()
  const getDatamatrixResponse = trpc.datamatrixResponses.findOne.useQuery(
    { id: parseInt(id!) },
    { enabled: id !== undefined },
  )
  const updateDatamatrixResponse =
    trpc.datamatrixResponses.updateOne.useMutation()

  if (id === undefined) {
    return <Typography>{t('datamatrix_responses.id_missing')}</Typography>
  }

  if (getDatamatrixResponse.isLoading) {
    return <Typography>{t('datamatrix_responses.loading')}</Typography>
  }

  if (getDatamatrixResponse.error) {
    return (
      <Typography className="flex gap-1">
        <span>{t('messages.error_loading_datamatrix_response')}</span>
        <span>{getDatamatrixResponse.error.message}</span>
      </Typography>
    )
  }

  if (getDatamatrixResponse.data === undefined) {
    return <Typography>{t('datamatrix_responses.not_found')}</Typography>
  }

  return (
    <div>
      <Form
        onSubmit={async event => {
          event.preventDefault()
          const formData = new FormData(event.currentTarget)
          await fetch(`/api/datamatrix-responses/${id}`, {
            method: 'POST',
            body: formData,
          })
          await getDatamatrixResponse.refetch()
          await utils.datamatrixResponses.findOne.invalidate({
            id: parseInt(id),
          })
        }}
      >
        <Input type="file" name="file" />
        <Button type="submit" children={t('common.submit')} />
      </Form>
      <DatamatrixResponseForm
        action={t('common.save')}
        title={t('datamatrix_responses.edit_title')}
        initialData={{ items: getDatamatrixResponse.data.items }}
        onSubmit={async data => {
          await updateDatamatrixResponse.mutateAsync({
            id: parseInt(id),
            items: data.items,
          })
          await utils.datamatrixResponses.findOne.invalidate({
            id: parseInt(id),
          })
        }}
      />
    </div>
  )
}

type DatamatrixResponseFormProps = {
  action: string
  title: string
  initialData?: {
    items?: Array<{ id?: number; product_unit_id: number; datamatrix: string }>
  }
  onSubmit: (data: DatamatrixResponseFormData) => Promise<void>
}
const DatamatrixResponseForm: ComponentType<DatamatrixResponseFormProps> = ({
  action,
  title,
  initialData,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const companyId = useCompanyId()

  const form = useForm<DatamatrixResponseFormData>({
    values: {
      items: initialData?.items || [{ product_unit_id: 0, datamatrix: '' }],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  })

  const getProductUnits = trpc.products.findUnits.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <div>
      <Header>{title}</Header>
      <nav>
        <RouteLink to={paths.datamatrixResponses} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Form
        onSubmit={form.handleSubmit(async data => {
          const validItems = data.items.filter(
            item => item.product_unit_id !== 0 && item.datamatrix.trim() !== '',
          )
          await onSubmit({ ...data, items: validItems })
          await navigate({
            pathname: paths.datamatrixResponses,
            search: searchParams.toString(),
          })
        })}
      >
        <div className="flex flex-col gap-2">
          <Typography>{t('datamatrix_responses.items_title')}</Typography>
          <Table>
            <thead>
              <tr>
                <HeadCell>{t('table_columns.product_unit')}</HeadCell>
                <HeadCell>{t('table_columns.datamatrix')}</HeadCell>
                <HeadCell>{t('table_columns.actions')}</HeadCell>
              </tr>
            </thead>
            <tbody>
              {fields.map((field, index) => (
                <tr key={field.id}>
                  <BodyCell>
                    <ComboBoxField
                      control={form.control}
                      name={`items.${index}.product_unit_id`}
                      options={
                        getProductUnits.data?.map(unit => ({
                          value: String(unit.id),
                          label: formatProductUnit(unit.product.name, unit),
                        })) || []
                      }
                      onChange={() => {
                        form.setFocus(`items.${index}.datamatrix`)
                      }}
                    />
                  </BodyCell>
                  <BodyCell>
                    <Input
                      className="w-full"
                      {...form.register(`items.${index}.datamatrix`)}
                    />
                  </BodyCell>
                  <BodyCell>
                    <Button
                      type="button"
                      onClick={() => remove(index)}
                      children={t('common.remove')}
                    />
                  </BodyCell>
                </tr>
              ))}
            </tbody>
          </Table>
          <div>
            <Button
              type="button"
              onClick={() => append({ product_unit_id: 0, datamatrix: '' })}
              children={t('common.add_item')}
            />
          </div>
          <Button type="submit" variant="primary" children={action} />
        </div>
      </Form>
    </div>
  )
}
