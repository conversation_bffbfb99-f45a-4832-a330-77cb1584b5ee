import { Button, Form, InputField, Typography } from '@/components/core'
import { useTranslation } from '@/i18n'
import { login } from '@/redux/slices/authSlice'
import { useAppDispatch } from '@/redux/store'
import { useState, type FC } from 'react'
import { useForm } from 'react-hook-form'

const values = { email: '', password: '' }

export const Login: FC = () => {
  const { t } = useTranslation()
  const dispatch = useAppDispatch()
  const [error, setError] = useState('')
  const form = useForm({ values })

  return (
    <Form
      onSubmit={form.handleSubmit(async values => {
        try {
          await dispatch(login(values)).unwrap()
        } catch (error) {
          setError(
            error instanceof Error ? error.message : t('common.unknown_error'),
          )
        }
      })}
    >
      <InputField
        control={form.control}
        name="email"
        type="email"
        label={t('common.email')}
      />
      <InputField
        control={form.control}
        name="password"
        type="password"
        label={t('common.password')}
      />
      {error && <Typography>{error}</Typography>}
      <Button type="submit" variant="primary">
        {t('common.login')}
      </Button>
    </Form>
  )
}
