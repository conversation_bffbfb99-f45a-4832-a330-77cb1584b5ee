import { paths } from '@/constants'
import { type RouteObject } from 'react-router'
import {
  CreateDatamatrixRequest,
  EditDatamatrixRequest,
} from './DatamatrixRequestForm'
import { DatamatrixRequests } from './DatamatrixRequests'
import { DatamatrixRequestsList } from './DatamatrixRequestsList'

export default {
  path: paths.datamatrixRequests,
  Component: DatamatrixRequests,
  children: [
    { path: paths.new, Component: CreateDatamatrixRequest },
    { path: paths.id, Component: EditDatamatrixRequest },
    { path: '', Component: DatamatrixRequestsList },
  ],
} satisfies RouteObject
