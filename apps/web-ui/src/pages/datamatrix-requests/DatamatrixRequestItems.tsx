import {
  <PERSON><PERSON>ell,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ell,
  <PERSON>er,
  Input,
  RouteLink,
  Select,
  Table,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatProductUnit } from '@/utils'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC, useState } from 'react'
import { useParams } from 'react-router'

type DatamatrixRequestItem =
  RouterOutputs['datamatrixRequests']['findItems'][number]

export const DatamatrixRequestItems: FC = () => {
  const { t } = useTranslation()
  const { id: datamatrixRequestId } = useParams()
  const companyId = useCompanyId()
  const utils = trpc.useUtils()
  const [editingItem, setEditingItem] = useState<DatamatrixRequestItem | null>(
    null,
  )
  const [newItem, setNewItem] = useState({ product_unit_id: 0, quantity: 1 })

  const getDatamatrixRequest = trpc.datamatrixRequests.findOne.useQuery(
    { id: parseInt(datamatrixRequestId!) },
    { enabled: datamatrixRequestId !== undefined },
  )
  const getDatamatrixRequestItems = trpc.datamatrixRequests.findItems.useQuery(
    { datamatrix_request_id: parseInt(datamatrixRequestId!) },
    { enabled: datamatrixRequestId !== undefined },
  )
  const getProducts = trpc.products.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const updateDatamatrixRequest =
    trpc.datamatrixRequests.updateOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  if (
    datamatrixRequestId === undefined ||
    getDatamatrixRequest.data === undefined
  ) {
    return <Typography>{t('common.loading')}</Typography>
  }

  const items = getDatamatrixRequestItems.data || []

  const handleAddItem = async () => {
    if (newItem.product_unit_id === 0 || newItem.quantity <= 0) return

    const updatedItems = [...items, newItem]
    await updateDatamatrixRequest.mutateAsync({
      id: parseInt(datamatrixRequestId),
      items: updatedItems,
    })

    await utils.datamatrixRequests.findOne.invalidate({
      id: parseInt(datamatrixRequestId),
    })
    await utils.datamatrixRequests.findItems.invalidate({
      datamatrix_request_id: parseInt(datamatrixRequestId),
    })
    setNewItem({ product_unit_id: 0, quantity: 1 })
  }

  const handleUpdateItem = async (
    itemId: number,
    updatedItem: Partial<DatamatrixRequestItem>,
  ) => {
    const updatedItems = items.map(item =>
      item.id === itemId ? { ...item, ...updatedItem } : item,
    )

    await updateDatamatrixRequest.mutateAsync({
      id: parseInt(datamatrixRequestId),
      items: updatedItems,
    })

    await utils.datamatrixRequests.findOne.invalidate({
      id: parseInt(datamatrixRequestId),
    })
    await utils.datamatrixRequests.findItems.invalidate({
      datamatrix_request_id: parseInt(datamatrixRequestId),
    })
    setEditingItem(null)
  }

  const handleDeleteItem = async (itemId: number) => {
    const updatedItems = items.filter(item => item.id !== itemId)
    await updateDatamatrixRequest.mutateAsync({
      id: parseInt(datamatrixRequestId),
      items: updatedItems,
    })
    await utils.datamatrixRequests.findOne.invalidate({
      id: parseInt(datamatrixRequestId),
    })
    await utils.datamatrixRequests.findItems.invalidate({
      datamatrix_request_id: parseInt(datamatrixRequestId),
    })
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.datamatrixRequests} end>
          {t('datamatrix_requests.back_to_datamatrix_requests')}
        </RouteLink>
      </nav>
      <Header>
        {t('datamatrix_requests.items_header', { id: datamatrixRequestId })}
      </Header>
      <div>
        <Typography>{t('datamatrix_requests.add_new_item')}</Typography>
        <div>
          <FormItem text={t('forms.product_unit')}>
            <Select
              value={newItem.product_unit_id.toString()}
              onChange={event => {
                setNewItem({
                  ...newItem,
                  product_unit_id: parseInt(event.target.value) || 0,
                })
              }}
            >
              <option value="0">{t('forms.select_product_unit')}</option>
              {getProducts.data?.flatMap(product =>
                product.product_units.map(unit => (
                  <option key={unit.id} value={unit.id}>
                    {formatProductUnit(product.name, unit)}
                  </option>
                )),
              )}
            </Select>
          </FormItem>
          <FormItem text={t('common.quantity')}>
            <Input
              type="number"
              min="0"
              value={newItem.quantity}
              onChange={event => {
                setNewItem({
                  ...newItem,
                  quantity: parseInt(event.target.value) || 0,
                })
              }}
            />
          </FormItem>
          <Button type="button" onClick={handleAddItem}>
            {t('common.add_item')}
          </Button>
        </div>
      </div>
      <div>
        <Typography>{t('datamatrix_requests.current_items')}</Typography>
        {items.length === 0 ? (
          <Typography>{t('datamatrix_requests.no_items')}</Typography>
        ) : (
          <Table>
            <thead>
              <tr>
                <HeadCell>{t('common.product')}</HeadCell>
                <HeadCell>{t('common.unit')}</HeadCell>
                <HeadCell>{t('common.barcode')}</HeadCell>
                <HeadCell>{t('common.quantity')}</HeadCell>
                <HeadCell>{t('common.actions')}</HeadCell>
              </tr>
            </thead>
            <tbody>
              {items.map(item => (
                <tr key={item.id}>
                  {editingItem?.id === item.id ? (
                    <>
                      <BodyCell colSpan={4}>
                        <FormItem text={t('forms.product_unit')}>
                          <Select
                            value={editingItem.product_unit_id || 0}
                            onChange={event => {
                              setEditingItem({
                                ...editingItem,
                                product_unit_id:
                                  parseInt(event.target.value) || 0,
                              })
                            }}
                          >
                            <option value="">
                              {t('forms.select_product_unit')}
                            </option>
                            {getProducts.data?.flatMap(product =>
                              product.product_units.map(unit => (
                                <option key={unit.id} value={unit.id}>
                                  {product.name} - {unit.name} (x
                                  {unit.multiplier}) ({unit.barcode})
                                </option>
                              )),
                            )}
                          </Select>
                        </FormItem>
                        <FormItem text={t('common.quantity')}>
                          <Input
                            type="number"
                            min="0"
                            value={editingItem.quantity || 0}
                            onChange={event => {
                              setEditingItem({
                                ...editingItem,
                                quantity: parseInt(event.target.value) || 0,
                              })
                            }}
                          />
                        </FormItem>
                      </BodyCell>
                      <BodyCell>
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            onClick={() =>
                              handleUpdateItem(item.id, editingItem)
                            }
                          >
                            {t('common.save')}
                          </Button>
                          <Button
                            type="button"
                            onClick={() => {
                              setEditingItem(null)
                            }}
                          >
                            {t('common.cancel')}
                          </Button>
                        </div>
                      </BodyCell>
                    </>
                  ) : (
                    <>
                      <BodyCell>{item.product_unit.product.name}</BodyCell>
                      <BodyCell>{item.product_unit.name}</BodyCell>
                      <BodyCell>{item.product_unit.barcode}</BodyCell>
                      <BodyCell>{item.quantity}</BodyCell>
                      <BodyCell>
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            onClick={() => {
                              setEditingItem(item)
                            }}
                          >
                            {t('common.edit')}
                          </Button>
                          <Button
                            type="button"
                            onClick={() => handleDeleteItem(item.id)}
                          >
                            {t('common.delete')}
                          </Button>
                        </div>
                      </BodyCell>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </Table>
        )}
      </div>
    </div>
  )
}
