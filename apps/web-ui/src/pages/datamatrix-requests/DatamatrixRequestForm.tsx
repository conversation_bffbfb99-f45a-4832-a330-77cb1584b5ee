import { ComboBoxField } from '@/components'
import {
  BodyCell,
  Button,
  Form,
  HeadCell,
  Header,
  Input,
  RouteLink,
  Table,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { formatProductUnit } from '@/utils'
import { type ComponentType, type FC } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

type DatamatrixRequestItem = {
  id?: number
  product_unit_id: number
  quantity: number
}

type DatamatrixRequestFormData = { items: Array<DatamatrixRequestItem> }

export const CreateDatamatrixRequest: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createDatamatrixRequest =
    trpc.datamatrixRequests.createOne.useMutation()

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <DatamatrixRequestForm
      action={t('common.create')}
      title={t('datamatrix_requests.create_title')}
      onSubmit={async data => {
        await createDatamatrixRequest.mutateAsync({
          company_id: companyId,
          items: data.items,
        })
      }}
    />
  )
}

export const EditDatamatrixRequest: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const utils = trpc.useUtils()
  const updateDatamatrixRequest =
    trpc.datamatrixRequests.updateOne.useMutation()
  const getDatamatrixRequest = trpc.datamatrixRequests.findOne.useQuery(
    { id: parseInt(id!) },
    { enabled: id !== undefined },
  )

  if (id === undefined) {
    return <Typography>{t('datamatrix_requests.id_missing')}</Typography>
  }

  if (getDatamatrixRequest.isLoading) {
    return <Typography>{t('datamatrix_requests.loading')}</Typography>
  }

  if (getDatamatrixRequest.error) {
    return (
      <Typography className="flex gap-1">
        <span>{t('messages.error_loading_datamatrix_request')}</span>
        <span>{getDatamatrixRequest.error.message}</span>
      </Typography>
    )
  }

  if (getDatamatrixRequest.data === undefined) {
    return <Typography>{t('datamatrix_requests.not_found')}</Typography>
  }

  return (
    <DatamatrixRequestForm
      action={t('common.save')}
      title={t('datamatrix_requests.edit_title')}
      initialData={{ items: getDatamatrixRequest.data.items }}
      onSubmit={async data => {
        await updateDatamatrixRequest.mutateAsync({
          id: parseInt(id),
          items: data.items,
        })
        await utils.datamatrixRequests.findOne.invalidate({ id: parseInt(id) })
      }}
    />
  )
}

type DatamatrixRequestFormProps = {
  action: string
  title: string
  initialData?: {
    items?: Array<{ id?: number; product_unit_id: number; quantity: number }>
  }
  onSubmit: (data: DatamatrixRequestFormData) => Promise<void>
}
const DatamatrixRequestForm: ComponentType<DatamatrixRequestFormProps> = ({
  action,
  title,
  initialData,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const companyId = useCompanyId()

  const form = useForm<DatamatrixRequestFormData>({
    values: {
      items: initialData?.items || [{ product_unit_id: 0, quantity: 1 }],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  })

  const getProductUnits = trpc.products.findUnits.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  if (companyId === null) {
    return <Typography>{t('messages.please_select_company')}</Typography>
  }

  return (
    <div>
      <Header>{title}</Header>
      <nav>
        <RouteLink to={paths.datamatrixRequests} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Form
        onSubmit={form.handleSubmit(async data => {
          const validItems = data.items.filter(
            item => item.product_unit_id !== 0 && item.quantity > 0,
          )
          await onSubmit({ ...data, items: validItems })
          await navigate({
            pathname: paths.datamatrixRequests,
            search: searchParams.toString(),
          })
        })}
      >
        <div className="flex flex-col gap-2">
          <Typography>{t('datamatrix_requests.items_title')}</Typography>
          <Table>
            <thead>
              <tr>
                <HeadCell>{t('forms.product_unit')}</HeadCell>
                <HeadCell>{t('common.quantity')}</HeadCell>
                <HeadCell>{t('common.actions')}</HeadCell>
              </tr>
            </thead>
            <tbody>
              {fields.map((field, index) => (
                <tr key={field.id}>
                  <BodyCell>
                    <ComboBoxField
                      className="w-full"
                      control={form.control}
                      name={`items.${index}.product_unit_id`}
                      options={
                        getProductUnits.data?.map(unit => ({
                          value: String(unit.id),
                          label: formatProductUnit(unit.product.name, unit),
                        })) || []
                      }
                      onChange={() => {
                        form.setFocus(`items.${index}.quantity`)
                      }}
                    />
                  </BodyCell>
                  <BodyCell>
                    <Input
                      className="w-full text-right"
                      type="number"
                      onFocus={event => void event.target.select()}
                      {...form.register(`items.${index}.quantity`, {
                        valueAsNumber: true,
                      })}
                    />
                  </BodyCell>
                  <BodyCell>
                    <Button
                      type="button"
                      onClick={() => void remove(index)}
                      children={t('common.remove')}
                    />
                  </BodyCell>
                </tr>
              ))}
            </tbody>
          </Table>
          <div>
            <Button
              type="button"
              onClick={() => void append({ product_unit_id: 0, quantity: 1 })}
              children={t('common.add_item')}
            />
          </div>
        </div>
        <Button type="submit" variant="primary" children={action} />
      </Form>
    </div>
  )
}
