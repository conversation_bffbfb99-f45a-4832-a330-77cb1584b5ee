import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type DatamatrixRequest = RouterOutputs['datamatrixRequests']['findMany'][number]

export const DatamatrixRequestsList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const getDatamatrixRequests = trpc.datamatrixRequests.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<DatamatrixRequest>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('pages.datamatrix_requests.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getDatamatrixRequests.data || []} columns={columns} />
    </div>
  )
}
