import { Button, Form, InputField } from '@/components/core'
import { paths } from '@/constants'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate } from 'react-router'

const values = { email: '', password: '' }

export const Register: FC = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const form = useForm({ values })
  const registerUser = trpc.users.register.useMutation()

  return (
    <Form
      onSubmit={form.handleSubmit(async values => {
        await registerUser.mutateAsync(values)
        void navigate(paths.root)
      })}
    >
      <InputField
        control={form.control}
        name="email"
        type="email"
        label={t('common.email')}
      />
      <InputField
        control={form.control}
        name="password"
        type="password"
        label={t('common.password')}
      />
      <Button type="submit" variant="primary">
        {t('common.register')}
      </Button>
    </Form>
  )
}
