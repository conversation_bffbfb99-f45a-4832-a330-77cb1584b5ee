import { ListTable, type Column } from '@/components'
import {
  Button,
  Form,
  FormItem,
  Header,
  Input,
  Typography,
} from '@/components/core'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { useState, type FC } from 'react'
import { useForm } from 'react-hook-form'

type ContentItem = RouterOutputs['datamatrixResponses']['findContent'][number]

type DatamatrixItemInfoProps = { datamatrix: string | null }
const DatamatrixItemInfo: FC<DatamatrixItemInfoProps> = ({ datamatrix }) => {
  const { t } = useTranslation()
  const getItemQuery = trpc.datamatrixResponses.findItem.useQuery(
    { datamatrix: datamatrix! },
    { enabled: datamatrix !== null },
  )

  const getContentQuery = trpc.datamatrixResponses.findContent.useQuery(
    { id: getItemQuery.data?.id ?? 0 },
    { enabled: !!getItemQuery.data?.id },
  )

  return (
    <div className="flex flex-col gap-2">
      {getItemQuery.isLoading && (
        <Typography>{t('common.loading_item')}</Typography>
      )}
      {(getItemQuery.isError ||
        (!getItemQuery.data && !getItemQuery.isLoading && datamatrix)) && (
        <Typography>{t('common.item_not_found')}</Typography>
      )}
      {getItemQuery.data && (
        <div className="border border-gray-300 p-2">
          <Typography>{getItemQuery.data.product_unit.product.name}</Typography>
          <Typography>{getItemQuery.data.product_unit.name}</Typography>
          <Typography>{getItemQuery.data.product_unit.barcode}</Typography>
          {getContentQuery.data && (
            <Typography>
              <span>{getContentQuery.data.length}</span>
              <span>/</span>
              <span>{getItemQuery.data.product_unit.multiplier}</span>
            </Typography>
          )}
        </div>
      )}
    </div>
  )
}

type ContainerContentProps = { id: number | null }
const ContainerContent: FC<ContainerContentProps> = ({ id }) => {
  const { t } = useTranslation()
  const getContentQuery = trpc.datamatrixResponses.findContent.useQuery(
    { id: id ?? 0 },
    { enabled: !!id },
  )

  const { data, isLoading, isError } = getContentQuery
  const columns: Array<Column<ContentItem>> = [
    { title: t('common.id'), cell: row => row.id },
    { title: t('common.datamatrix'), cell: row => row.datamatrix },
    { title: t('common.product'), cell: row => row.product_unit.product.name },
    { title: t('common.unit'), cell: row => row.product_unit.name },
    { title: t('common.barcode'), cell: row => row.product_unit.barcode },
  ]

  return (
    <div className="flex flex-col gap-2">
      {isLoading && (
        <Typography>{t('common.loading_container_content')}</Typography>
      )}
      {isError && (
        <Typography>{t('common.error_loading_container_content')}</Typography>
      )}
      {data && data.length > 0 && (
        <div>
          <Typography className="mb-2 font-semibold">
            {t('common.container_content')}
          </Typography>
          <ListTable rows={data} columns={columns} />
        </div>
      )}
      {data && data.length === 0 && (
        <Typography>{t('common.container_is_empty')}</Typography>
      )}
    </div>
  )
}

export const Packaging: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const [containerDatamatrix, setContainerDatamatrix] = useState<string | null>(
    null,
  )

  const containerForm = useForm({ values: { datamatrix: '' } })

  const itemForm = useForm({ values: { datamatrix: '' } })

  const setContainerMutation =
    trpc.datamatrixResponses.setContainer.useMutation({
      onSuccess: () => {
        itemForm.reset()
        void getItemQuery.refetch()
        void getContentQuery.refetch()
      },
    })

  const getItemQuery = trpc.datamatrixResponses.findItem.useQuery(
    { datamatrix: containerDatamatrix! },
    { enabled: containerDatamatrix !== null },
  )

  const getContentQuery = trpc.datamatrixResponses.findContent.useQuery(
    { id: getItemQuery.data?.id ?? 0 },
    { enabled: !!getItemQuery.data?.id },
  )

  const isContainerFull =
    getItemQuery.data && getContentQuery.data
      ? getContentQuery.data.length >= getItemQuery.data.product_unit.multiplier
      : false

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div className="flex flex-col gap-2">
      <Header>{t('packaging.title')}</Header>
      <div className="flex flex-col">
        <Form
          onSubmit={containerForm.handleSubmit(({ datamatrix }) => {
            if (!datamatrix) return
            setContainerDatamatrix(datamatrix)
          })}
        >
          <FormItem text={t('common.container')}>
            <div className="flex gap-2">
              <Input
                className="grow"
                {...containerForm.register('datamatrix')}
              />
              <Button type="submit" variant="primary">
                {t('common.open')}
              </Button>
            </div>
          </FormItem>
        </Form>
        <DatamatrixItemInfo datamatrix={containerDatamatrix} />
        <ContainerContent id={getItemQuery.data?.id ?? null} />
      </div>
      <Form
        onSubmit={itemForm.handleSubmit(data => {
          if (!containerDatamatrix || !data.datamatrix) return
          setContainerMutation.mutate({
            container_datamatrix: containerDatamatrix,
            item_datamatrix: data.datamatrix,
          })
        })}
      >
        <FormItem text={t('common.item')}>
          <div className="flex gap-2">
            <Input
              className="grow"
              disabled={isContainerFull}
              {...itemForm.register('datamatrix')}
            />
            <Button type="submit" variant="primary" disabled={isContainerFull}>
              {t('common.add')}
            </Button>
          </div>
        </FormItem>
        {setContainerMutation.isError && (
          <Typography className="text-red-600">
            {setContainerMutation.error.message}
          </Typography>
        )}
      </Form>
    </div>
  )
}
