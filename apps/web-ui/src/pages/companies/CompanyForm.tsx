import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RouteLink } from '@/components/core'
import { paths } from '@/constants'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { type ComponentType, type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router'

export const CreateCompany: FC = () => {
  const { t } = useTranslation()
  const createCompany = trpc.companies.createOne.useMutation()

  return (
    <CompanyForm
      action={t('common.create')}
      title={t('companies.create_title')}
      values={{ name: '' }}
      onSubmit={async values => {
        await createCompany.mutateAsync(values)
      }}
    />
  )
}

export const EditCompany: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const updateCompany = trpc.companies.updateOne.useMutation()
  const getCompany = trpc.companies.findOne.useQuery(
    { id: parseInt(id!) },
    { enabled: id !== undefined },
  )

  if (id === undefined) return null
  if (getCompany.isLoading) return null
  if (getCompany.data === undefined) return null

  return (
    <CompanyForm
      action={t('common.save')}
      title={t('companies.edit_title')}
      values={{ name: getCompany.data.name }}
      onSubmit={async values => {
        await updateCompany.mutateAsync({ id: parseInt(id), ...values })
      }}
    />
  )
}

type Company = { name: string }
type CompanyFormProps = {
  action: string
  title: string
  values: Company
  onSubmit: (values: Company) => Promise<void>
}
const CompanyForm: ComponentType<CompanyFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const form = useForm({ values })

  return (
    <div>
      <nav>
        <RouteLink to={paths.companies} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
          await navigate({
            pathname: paths.companies,
            search: searchParams.toString(),
          })
        })}
      >
        <InputField
          control={form.control}
          name="name"
          type="text"
          label={t('common.name')}
        />
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
