import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header } from '@/components/core'
import { paths } from '@/constants'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type Company = RouterOutputs['companies']['findMany'][number]

export const CompaniesList: FC = () => {
  const { t } = useTranslation()
  const getCompanies = trpc.companies.findMany.useQuery()

  const columns: Array<Column<Company>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.name'), cell: row => row.name },
  ]

  return (
    <div>
      <Header>{t('pages.companies.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getCompanies.data} columns={columns} />
    </div>
  )
}
