import { type RouteObject } from 'react-router'
import { CreatePayment, EditPayment } from './PaymentForm'
import { Payments } from './Payments'
import { PaymentsList } from './PaymentsList'

export default {
  path: 'payments',
  Component: Payments,
  children: [
    { path: 'new', Component: CreatePayment },
    { path: ':id', Component: EditPayment },
    { path: '', Component: PaymentsList },
  ],
} satisfies RouteObject
