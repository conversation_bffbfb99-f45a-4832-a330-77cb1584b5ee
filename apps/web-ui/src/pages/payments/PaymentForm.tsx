import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>er,
  InputField,
  NumberInputField,
  RadioGroupField,
  RouteLink,
  SelectField,
  Typography,
} from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import { type ComponentType, type FC } from 'react'
import { useForm } from 'react-hook-form'
import { useNavigate, useParams } from 'react-router'

type Payment = {
  business_party_id: number
  date: string
  amount: number
  direction: 'incoming' | 'outgoing'
}

export const CreatePayment: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const createPayment = trpc.payments.createOne.useMutation()
  const navigate = useNavigate()

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <PaymentForm
      action={t('common.create')}
      title={t('payments.create_title')}
      values={{
        business_party_id: 0,
        amount: 0,
        direction: 'incoming',
        date: new Date().toISOString().split('T')[0],
      }}
      onSubmit={async values => {
        await createPayment.mutateAsync({
          business_party_id: values.business_party_id,
          amount: values.amount,
          direction: values.direction,
          date: values.date,
          company_id: companyId,
        })
        void navigate('..')
      }}
    />
  )
}
export const EditPayment: FC = () => {
  const { t } = useTranslation()
  const { id } = useParams()
  const companyId = useCompanyId()
  const updatePayment = trpc.payments.updateOne.useMutation()
  const getPayment = trpc.payments.findOne.useQuery(
    { id: parseInt(id!), company_id: companyId! },
    { enabled: id !== undefined && companyId !== null },
  )
  const navigate = useNavigate()

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  if (!getPayment.data) {
    return <Typography>{t('common.loading')}</Typography>
  }

  return (
    <PaymentForm
      action={t('common.update')}
      title={t('payments.edit_title')}
      values={{
        business_party_id: getPayment.data.business_party_id,
        amount: getPayment.data.amount,
        direction: getPayment.data.direction as 'incoming' | 'outgoing',
        date: getPayment.data.date,
      }}
      onSubmit={async values => {
        await updatePayment.mutateAsync({
          id: parseInt(id!),
          business_party_id: values.business_party_id,
          amount: values.amount,
          direction: values.direction,
          date: values.date,
        })
        void navigate('..')
      }}
    />
  )
}
type PaymentFormProps = {
  action: string
  title: string
  values: Payment
  onSubmit: (values: Payment) => Promise<void>
}

const PaymentForm: ComponentType<PaymentFormProps> = ({
  action,
  title,
  values,
  onSubmit,
}) => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const getBusinessParties = trpc.businessParties.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const form = useForm({ values })

  const handleBusinessPartyChange = (businessPartyId: number) => {
    if (!businessPartyId || businessPartyId === 0) return

    const selectedParty = getBusinessParties.data?.find(
      party => party.id === businessPartyId,
    )

    if (selectedParty) {
      if (selectedParty.is_supplier && !selectedParty.is_customer) {
        form.setValue('direction', 'outgoing')
      }
      if (selectedParty.is_customer && !selectedParty.is_supplier) {
        form.setValue('direction', 'incoming')
      }
    }
  }

  return (
    <div>
      <nav>
        <RouteLink to={paths.payments} end>
          {t('common.list')}
        </RouteLink>
      </nav>
      <Header>{title}</Header>
      <Form
        onSubmit={form.handleSubmit(async values => {
          await onSubmit(values)
        })}
      >
        <FormItem text={t('common.date')}>
          <InputField control={form.control} name="date" type="date" />
        </FormItem>
        <FormItem text={t('common.business_party')}>
          <SelectField
            control={form.control}
            name="business_party_id"
            options={[
              { value: 0, label: t('common.select') },
              ...(getBusinessParties.data?.map(party => ({
                value: party.id,
                label: party.name,
              })) || []),
            ]}
            onChange={handleBusinessPartyChange}
          />
        </FormItem>
        <FormItem text={t('common.direction')}>
          <RadioGroupField
            control={form.control}
            name="direction"
            options={[
              { value: 'incoming', label: t('common.incoming') },
              { value: 'outgoing', label: t('common.outgoing') },
            ]}
          />
        </FormItem>
        <FormItem text={t('common.amount')}>
          <NumberInputField control={form.control} name="amount" />
        </FormItem>
        <Button type="submit" variant="primary">
          {action}
        </Button>
      </Form>
    </div>
  )
}
