import { Identity, ListTable, RouteLinks, type Column } from '@/components'
import { Header, Typography } from '@/components/core'
import { paths } from '@/constants'
import { useCompanyId } from '@/hooks'
import { useTranslation } from '@/i18n'
import { trpc } from '@/trpc'
import type { RouterOutputs } from '@cloudretail/api'
import { type FC } from 'react'

type Payment = RouterOutputs['payments']['findMany'][number]

export const PaymentsList: FC = () => {
  const { t } = useTranslation()
  const companyId = useCompanyId()
  const getPayments = trpc.payments.findMany.useQuery(
    { company_id: companyId! },
    { enabled: companyId !== null },
  )

  const columns: Array<Column<Payment>> = [
    { title: t('common.id'), cell: row => <Identity id={row.id} /> },
    { title: t('common.amount'), cell: row => row.amount.toString() },

    { title: t('common.direction'), cell: row => t(`common.${row.direction}`) },
    {
      title: t('common.date'),
      cell: row => new Date(row.date).toLocaleDateString(),
    },
  ]

  if (companyId === null) {
    return <Typography>{t('messages.select_company')}</Typography>
  }

  return (
    <div>
      <Header>{t('pages.payments.title')}</Header>
      <RouteLinks links={[{ to: paths.new, label: t('common.new') }]} />
      <ListTable rows={getPayments.data || []} columns={columns} />
    </div>
  )
}
