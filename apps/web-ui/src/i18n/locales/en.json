{"business_party_inventories": {"create_title": "Create a new business party inventory", "edit_title": "Edit business party inventory", "not_found": "Business party inventory not found"}, "business_parties": {"create_title": "Create a new business party", "edit_title": "Edit a business party"}, "common": {"actions": "Actions", "add": "Add", "add_item": "Add Item", "add_new_item": "Add New Item", "amount": "Amount", "authorize": "Authorize", "back_to": "Back to", "barcode": "Barcode", "business_party": "Business Party", "cancel": "Cancel", "commit": "Commit", "container": "Container", "container_content": "Container Content:", "container_is_empty": "Container is empty", "create": "Create", "create_a_new": "Create a new", "current_items": "Current Items", "customer": "Customer", "datamatrix": "Datamatrix", "date": "Date", "delete": "Delete", "description": "Description", "direction": "Direction", "edit": "Edit", "edit_a": "Edit a", "email": "Email", "error_loading_container_content": "Error loading container content", "from_warehouse": "From Warehouse", "id": "ID", "incoming": "Incoming", "item": "<PERSON><PERSON>", "item_not_found": "Item not found", "items": "Items", "items_count": "Items Count", "list": "List", "loading": "Loading...", "loading_container_content": "Loading container content...", "loading_item": "Loading item...", "login": "<PERSON><PERSON>", "logout": "log out", "multiplier": "Multiplier", "name": "Name", "new": "New", "no_data": "No data", "no_items": "No items", "none": "None", "not_selected": "Not selected", "open": "Open", "outgoing": "Outgoing", "parent_unit": "Parent Unit", "password": "Password", "price": "Price", "product": "Product", "product_unit": "Product Unit", "productMovements": "Product Movements", "quantity": "Quantity", "register": "Register", "remove": "Remove", "save": "Save", "select": "Select", "select_a_product_unit": "Select a product unit", "submit": "Submit", "supplier": "Supplier", "to_warehouse": "To Warehouse", "total": "Total", "type": "Type", "unit": "Unit", "unit_name": "Unit Name", "units": "Units", "unknown_error": "Unknown error", "update": "Update", "warehouse": "Warehouse", "warehouse_transfer_not_found": "Warehouse transfer not found"}, "companies": {"create_title": "Create a new company", "edit_title": "Edit a company"}, "datamatrix_requests": {"add_new_item": "Add New Item", "back_to_datamatrix_requests": "Back to Datamatrix requests", "create_title": "Create a new datamatrix request", "current_items": "Current Items", "edit_title": "Edit a datamatrix request", "id_missing": "Datamatrix request id is missing", "items_header": "Datamatrix request Items - Request {id}", "items_title": "Datamatrix request Items", "loading": "Loading datamatrix request...", "no_items": "No items", "not_found": "Datamatrix request not found"}, "datamatrix_responses": {"add_new_item": "Add New Item", "authorize_user_title": "Authorize a user", "authorized_users_title": "Authorized users", "back_to_datamatrix_responses": "Back to Datamatrix responses", "create_title": "Create a new datamatrix response", "current_items": "Current Items", "edit_title": "Edit a datamatrix response", "id_missing": "Datamatrix response id is missing", "items_header": "Datamatrix response Items - Response {id}", "items_title": "Datamatrix response Items", "loading": "Loading datamatrix response...", "not_found": "Datamatrix response not found", "purchase_invoices_title": "Purchase Invoices", "warehouses_title": "Warehouses"}, "forms": {"enter_datamatrix_code": "Enter datamatrix code", "from_to_warehouses_same": "From and to warehouses cannot be the same", "please_add_at_least_one_item": "Please add at least one valid item", "please_select_business_party": "Please select a business party", "please_select_customer": "Please select a customer", "please_select_from_warehouse": "Please select a from warehouse", "please_select_supplier": "Please select a supplier", "please_select_to_warehouse": "Please select a to warehouse", "please_select_warehouse": "Please select a warehouse", "product_unit": "Product Unit", "select_business_party": "Select a business party", "select_customer": "Select a customer", "select_destination_warehouse": "Select destination warehouse", "select_product_unit": "Select a product unit", "select_source_warehouse": "Select source warehouse", "select_supplier": "Select a supplier", "select_warehouse": "Select a warehouse", "source_destination_different": "Source and destination warehouses must be different"}, "layout": {"sidebar": {"business_parties": "Business Parties", "business_party_inventories": "Business Party Inventories", "companies": "Companies", "datamatrix_requests": "Datamatrix requests", "datamatrix_responses": "Datamatrix responses", "packaging": "Packaging", "payment_accounts": "Payment Accounts", "payments": "Payments", "price_adjustments": "Price Adjustments", "prices": "Prices", "product_inventories": "Product Inventories", "products": "Products", "purchase_invoices": "Purchase Invoices", "sales_invoices": "Sales Invoices", "users": "Users", "warehouse_transfers": "Warehouse Transfers", "warehouses": "Warehouses"}}, "messages": {"error_deleting_item": "Error deleting item:", "error_loading_datamatrix_request": "Error loading datamatrix request:", "error_loading_datamatrix_response": "Error loading datamatrix response:", "please_select_company": "Please, select a company", "product_id_required": "Product ID is required", "select_company": "Please, select a company"}, "packaging": {"error_container": "Error loading container content", "loading_container": "Loading container content...", "loading_item": "Loading item...", "title": "Packaging"}, "pages": {"business_parties": {"create_title": "Create a new business party", "edit_title": "Edit a business party", "title": "Business Parties"}, "business_party_inventories": {"title": "Business Party Inventories"}, "companies": {"title": "Companies"}, "datamatrix_requests": {"title": "Datamatrix requests"}, "datamatrix_responses": {"title": "Datamatrix responses"}, "payment_accounts": {"title": "Payment Accounts"}, "payments": {"title": "Payments"}, "price_adjustments": {"title": "Price Adjustments"}, "prices": {"title": "Prices"}, "product_inventories": {"title": "Product Inventories"}, "products": {"title": "Products"}, "purchase_invoices": {"title": "Purchase Invoices"}, "sales_invoices": {"title": "Sales Invoices"}, "warehouses": {"title": "Warehouses"}}, "payment_accounts": {"create_title": "Create a new payment account", "edit_title": "Edit payment account", "type_bank": "Bank", "type_cash": "Cash"}, "payments": {"create_title": "Create a new payment", "edit_title": "Edit payment"}, "price_adjustments": {"create_title": "Create a new price adjustment", "edit_title": "Edit price adjustment", "not_found": "Price adjustment not found"}, "prices": {"create_title": "Create a new price", "edit_title": "Edit price", "type_purchase": "Purchase", "type_sales": "Sales"}, "product_inventories": {"create_title": "Create a new product inventory", "edit_title": "Edit product inventory", "items_title": "Product Inventory Items", "not_found": "Product inventory not found"}, "purchase_invoices": {"items_title": "Purchase Invoice Items", "not_found": "Purchase invoice not found"}, "sales_invoices": {"items_title": "Sales Invoice Items", "not_found": "Sales invoice not found"}, "table_columns": {"actions": "Actions", "amount": "Amount", "barcode": "Barcode", "datamatrix": "Datamatrix", "date": "Date", "email": "Email", "from_warehouse": "From Warehouse", "items": "Items", "name": "Name", "price": "Price", "product": "Product", "product_unit": "Product Unit", "supplier": "Supplier", "to_warehouse": "To Warehouse", "total": "Total", "unit": "Unit", "warehouse": "Warehouse"}, "warehouse_transfers": {"create_title": "Create a new warehouse transfer", "edit_title": "Edit warehouse transfer", "items_title": "Warehouse Transfer Items", "title": "Warehouse Transfers"}, "warehouses": {"create_title": "Create a new warehouse", "edit_title": "Edit a warehouse"}, "not_found": {"title": "Page not found", "description": "Sorry, we couldn't find the page you're looking for.", "go_home": "Go back home", "go_back": "Go back", "contact_support": "If you think this is an error, please contact support."}}