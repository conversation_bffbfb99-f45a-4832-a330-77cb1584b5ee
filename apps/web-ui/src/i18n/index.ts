import { createInstance } from '@ravshansbox/react-i18n'
import en from './locales/en.json'
import ru from './locales/ru.json'

const getDefaultLanguage = (): 'en' | 'ru' => {
  const urlSearchParams = new URLSearchParams(window.location.search)
  const language = urlSearchParams.get('language')
  return language === 'en' || language === 'ru' ? language : 'en'
}

export const { useTranslation } = createInstance({
  supportedLanguages: ['en', 'ru'],
  defaultLanguage: getDefaultLanguage(),
  translations: { en, ru },
})
