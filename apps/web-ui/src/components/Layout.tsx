import { type ComponentType, type PropsWithChildren } from 'react'
import { Navbar, Sidebar } from '.'

export const Layout: ComponentType<PropsWithChildren> = ({ children }) => {
  return (
    <div className="flex min-h-screen flex-col gap-2 p-2">
      <Navbar />
      <div className="flex grow gap-2">
        <div className="border border-gray-300 p-4">
          <Sidebar />
        </div>
        <div className="grow border border-gray-300 p-4">{children}</div>
      </div>
    </div>
  )
}
