import { useAppSelector } from '@/redux/store'
import { trpc } from '@/trpc'
import { type ChangeEvent, type FC } from 'react'
import { useSearchParams } from 'react-router'
import { Options, Select } from './core'

export const CompanySelect: FC = () => {
  const [searchParams, setSearchParams] = useSearchParams()
  const isAuthenticated = useAppSelector(state => state.auth.isAuthenticated)
  const companies = trpc.companies.findMany.useQuery(undefined, {
    enabled: isAuthenticated,
  })

  if (companies.data === undefined) return null

  return (
    <Select
      name="selected-company"
      value={searchParams.get('company_id') ?? ''}
      onChange={({ target: { value } }: ChangeEvent<HTMLSelectElement>) => {
        setSearchParams(
          currentSearchParams => {
            const nextSearchParams = new URLSearchParams(currentSearchParams)
            if (value === '0') {
              nextSearchParams.delete('company_id')
            } else {
              nextSearchParams.set('company_id', value)
            }
            return nextSearchParams
          },
          { replace: true },
        )
      }}
    >
      <Options items={companies.data} />
    </Select>
  )
}
