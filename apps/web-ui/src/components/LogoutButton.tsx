import { paths } from '@/constants'
import { useTranslation } from '@/i18n'
import { logout } from '@/redux/slices/authSlice'
import { useAppDispatch } from '@/redux/store'
import { type FC } from 'react'
import { useNavigate } from 'react-router'
import { Button } from './core'

export const LogoutButton: FC = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const dispatch = useAppDispatch()

  return (
    <Button
      type="submit"
      onClick={async () => {
        await dispatch(logout())
        await navigate(paths.root)
      }}
    >
      {t('common.logout')}
    </Button>
  )
}
