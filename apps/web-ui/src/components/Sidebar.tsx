import { paths } from '@/constants'
import { useTranslation } from '@/i18n'
import { type FC } from 'react'
import { List, ListItem, RouteLink } from './core'

export const Sidebar: FC = () => {
  const { t } = useTranslation()

  const links = [
    { label: t('layout.sidebar.packaging'), path: paths.packaging },
    {
      label: t('layout.sidebar.datamatrix_responses'),
      path: paths.datamatrixResponses,
    },
    {
      label: t('layout.sidebar.datamatrix_requests'),
      path: paths.datamatrixRequests,
    },
    { label: t('layout.sidebar.sales_invoices'), path: paths.salesInvoices },
    {
      label: t('layout.sidebar.purchase_invoices'),
      path: paths.purchaseInvoices,
    },
    {
      label: t('layout.sidebar.warehouse_transfers'),
      path: paths.warehouseTransfers,
    },
    { label: t('layout.sidebar.payments'), path: paths.payments },
    {
      label: t('layout.sidebar.price_adjustments'),
      path: paths.priceAdjustments,
    },
    {
      label: t('layout.sidebar.product_inventories'),
      path: paths.productInventories,
    },
    {
      label: t('layout.sidebar.business_party_inventories'),
      path: paths.businessPartyInventories,
    },
    { label: t('layout.sidebar.prices'), path: paths.prices },
    { label: t('layout.sidebar.products'), path: paths.products },
    {
      label: t('layout.sidebar.business_parties'),
      path: paths.businessParties,
    },
    {
      label: t('layout.sidebar.payment_accounts'),
      path: paths.paymentAccounts,
    },
    { label: t('layout.sidebar.warehouses'), path: paths.warehouses },
    { label: t('layout.sidebar.users'), path: paths.authorizedUsers },
    { label: t('layout.sidebar.companies'), path: paths.companies },
  ]

  return (
    <List className="flex-col gap-2">
      {links.map(({ label, path }) => (
        <ListItem key={path}>
          <RouteLink to={path}>{label}</RouteLink>
        </ListItem>
      ))}
    </List>
  )
}
