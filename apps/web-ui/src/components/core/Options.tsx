import { useTranslation } from '@/i18n'
import { type ComponentType } from 'react'

type Item = { id: number; name: string }

type OptionsProps = { items: Array<Item> | undefined }
export const Options: ComponentType<OptionsProps> = ({ items }) => {
  const { t } = useTranslation()
  const notSelected: Item = { id: 0, name: t('common.not_selected') }

  if (items === undefined || items.length === 0) return null

  return [notSelected, ...items].map(item => (
    <option key={item.id} value={item.id}>
      {item.name}
    </option>
  ))
}
