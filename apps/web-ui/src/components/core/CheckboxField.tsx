import {
  Controller,
  type Control,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form'

type CheckboxFieldProps<T extends FieldValues> = {
  control: Control<T>
  name: FieldPath<T>
  label: string
}

export const CheckboxField = <T extends FieldValues>({
  control,
  name,
  label,
}: CheckboxFieldProps<T>) => {
  return (
    <label className="flex items-center gap-2">
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <input
            type="checkbox"
            checked={field.value}
            onChange={field.onChange}
          />
        )}
      />
      {label}
    </label>
  )
}
