import { type Ref } from 'react'
import {
  Controller,
  type Control,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form'
import { NumericFormat } from 'react-number-format'

type NumberInputFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  control: Control<TFieldValues>
  name: TName
  className?: string
  decimalScale?: number
  fixedDecimalScale?: boolean
  allowNegative?: boolean
  thousandSeparator?: string | boolean
  readOnly?: boolean
  inputRef?: Ref<HTMLInputElement>
}

export const NumberInputField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  className = 'w-full border border-gray-300 px-2 py-1 text-right',
  decimalScale = 2,
  fixedDecimalScale = false,
  allowNegative = false,
  thousandSeparator = ',',
  readOnly = false,
  inputRef,
}: NumberInputFieldProps<TFieldValues, TName>) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <NumericFormat
          className={className}
          name={field.name}
          onBlur={field.onBlur}
          thousandSeparator={thousandSeparator}
          allowNegative={allowNegative}
          decimalScale={decimalScale}
          fixedDecimalScale={fixedDecimalScale}
          readOnly={readOnly}
          onFocus={event => void event.target.select()}
          value={field.value as number}
          onValueChange={values => {
            field.onChange(values.floatValue || 0)
          }}
          getInputRef={(element: HTMLInputElement | null) => {
            field.ref(element)
            if (typeof inputRef === 'function') {
              inputRef(element)
            } else if (inputRef && typeof inputRef === 'object') {
              inputRef.current = element
            }
          }}
        />
      )}
    />
  )
}
