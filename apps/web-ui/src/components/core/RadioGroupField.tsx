import {
  Controller,
  type Control,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form'

export type RadioGroupFieldOption<Value> = {
  value: Value
  label: string
  disabled?: boolean
}

type RadioGroupFieldProps<
  Value extends string | number,
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  control: Control<TFieldValues>
  name: TName
  options: Array<RadioGroupFieldOption<Value>>
  className?: string
}

export const RadioGroupField = <
  Value extends string | number,
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  options,
  className,
}: RadioGroupFieldProps<Value, TFieldValues, TName>) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <div className={className}>
          {options.map(option => (
            <label
              key={String(option.value)}
              className="flex items-center gap-2"
            >
              <input
                type="radio"
                value={String(option.value)}
                checked={field.value === option.value}
                onChange={() => field.onChange(option.value)}
                disabled={option.disabled}
              />
              {option.label}
            </label>
          ))}
        </div>
      )}
    />
  )
}
