import { type SelectHTMLAttributes } from 'react'
import {
  Controller,
  type Control,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form'
import { SelectCore } from './SelectCore'

export type SelectFieldOption<Value> = {
  value: Value
  label: string
  disabled?: boolean
}

export type SelectFieldProps<
  Value extends string | number | null,
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = Omit<
  SelectHTMLAttributes<HTMLSelectElement>,
  'name' | 'value' | 'onChange'
> & {
  control: Control<TFieldValues>
  name: TName
  options: Array<SelectFieldOption<Value>>
  onChange?: (value: number) => void
}

export const SelectField = <
  Value extends string | number | null,
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  options,
  onChange,
  ...props
}: SelectFieldProps<Value, TFieldValues, TName>) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <SelectCore
          {...props}
          {...field}
          options={options}
          onChange={value => {
            field.onChange(value)
            if (onChange && typeof value === 'number') {
              onChange(value)
            }
          }}
        />
      )}
    />
  )
}
