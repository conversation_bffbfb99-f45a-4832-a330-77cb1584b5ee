import { cn } from '@/utils'
import { type ComponentType } from 'react'
import { NavLink, useSearchParams, type NavLinkProps } from 'react-router'

type RouteLinkProps = Omit<NavLinkProps, 'to' | 'className'> & {
  to: string
  className?: string
}
export const RouteLink: ComponentType<RouteLinkProps> = ({
  to,
  className,
  ...props
}) => {
  const [searchParams] = useSearchParams()

  return (
    <NavLink
      to={{ pathname: to, search: searchParams.toString() }}
      className={({ isActive }) =>
        cn('text-blue-600', isActive && 'underline', className)
      }
      {...props}
    />
  )
}
