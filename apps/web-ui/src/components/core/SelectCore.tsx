import { type SelectHTMLAttributes } from 'react'
import { Select } from './Select'
import { type SelectFieldOption } from './SelectField'

export const SelectCore = <Value extends string | number | null>({
  options,
  value,
  onChange,
  ...props
}: {
  options: Array<SelectFieldOption<Value>>
  value?: Value
  onChange?: (value: Value) => void
} & Omit<SelectHTMLAttributes<HTMLSelectElement>, 'onChange' | 'value'>) => {
  return (
    <Select
      {...props}
      value={value ?? ''}
      onChange={event => {
        if (!onChange) return
        const { value } = event.target
        const option = options.find(
          option =>
            (option.value === null ? '' : String(option.value)) === value,
        )
        if (!option) return
        onChange(option.value)
      }}
    >
      {options.map(({ value, label, disabled }) => (
        <option
          key={String(value)}
          value={value === null ? '' : String(value)}
          disabled={disabled}
        >
          {label}
        </option>
      ))}
    </Select>
  )
}
