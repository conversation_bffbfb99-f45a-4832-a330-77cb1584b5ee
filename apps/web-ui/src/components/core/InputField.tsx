import { type HTMLInputTypeAttribute, type InputHTMLAttributes } from 'react'
import {
  Controller,
  type Control,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form'
import { FormItem, Input } from '.'

type InputFieldProps<T extends FieldValues> = {
  control: Control<T>
  name: FieldPath<T>
  type: HTMLInputTypeAttribute
  label?: string
  className?: string
  readOnly?: boolean
} & Omit<InputHTMLAttributes<HTMLInputElement>, 'name' | 'type'>

export const InputField = <T extends FieldValues>({
  control,
  name,
  type,
  label,
  className,
  readOnly,
  ...inputProps
}: InputFieldProps<T>) => {
  const inputElement = (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Input
          type={type}
          autoComplete="off"
          className={className}
          readOnly={readOnly}
          {...inputProps}
          {...field}
        />
      )}
    />
  )

  if (label) {
    return <FormItem text={label}>{inputElement}</FormItem>
  }

  return inputElement
}
