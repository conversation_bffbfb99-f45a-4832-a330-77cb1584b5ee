import { type ComponentType } from 'react'
import { List, ListItem, RouteLink } from './core'

type RouteLinksProps = {
  links: Array<{ to: string; label: string; hidden?: boolean }>
}
export const RouteLinks: ComponentType<RouteLinksProps> = ({ links }) => {
  return (
    <List>
      {links
        .filter(link => !link.hidden)
        .map(({ to, label }) => (
          <ListItem key={to}>
            <RouteLink to={to} end>
              {label}
            </RouteLink>
          </ListItem>
        ))}
    </List>
  )
}
