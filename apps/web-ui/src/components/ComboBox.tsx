import { useMemo, useRef, useState, type Ref } from 'react'
import {
  Controller,
  type Control,
  type FieldPath,
  type FieldValues,
} from 'react-hook-form'
import { Input, List, ListItem } from './core'

type Option = { value: string; label: string }

type ComboBoxProps = {
  options: Array<Option>
  ref?: Ref<HTMLInputElement>
  className?: string
  disabled?: boolean
  name: string
  value: string
  onChange: (value: string) => void
  onBlur?: () => void
}
export function ComboBox({
  options,
  ref,
  className,
  disabled,
  name,
  value,
  onChange,
  onBlur,
}: ComboBoxProps) {
  const [keyword, setKeyword] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const filteredOptions = useMemo(() => {
    const keywordLowerCase = keyword.trim().toLocaleLowerCase()
    return keywordLowerCase === ''
      ? options
      : options.filter(option =>
          option.label.toLocaleLowerCase().includes(keywordLowerCase),
        )
  }, [options, keyword])

  const selectedOption = useMemo(
    () => options.find(option => option.value === value),
    [options, value],
  )

  const valueLabel = selectedOption?.label ?? ''

  return (
    <div className={className}>
      <Input
        ref={ref}
        className="w-full"
        name={name}
        disabled={disabled}
        value={isOpen ? keyword : valueLabel}
        onChange={event => {
          setKeyword(event.target.value)
        }}
        onFocus={() => {
          setIsOpen(true)
        }}
        onBlur={event => {
          if (!dropdownRef.current?.contains(event.relatedTarget as Node)) {
            setKeyword('')
            setIsOpen(false)
            onBlur?.()
          }
        }}
      />
      <div className="relative" ref={dropdownRef}>
        {isOpen && (
          <List className="absolute flex flex-col border border-gray-300 bg-white">
            {filteredOptions.map(option => (
              <ListItem
                key={option.value}
                className="cursor-pointer px-2 py-1 whitespace-nowrap hover:bg-gray-200"
                tabIndex={-1}
                onClick={() => {
                  setIsOpen(false)
                  onChange(option.value)
                }}
              >
                {option.label}
              </ListItem>
            ))}
          </List>
        )}
      </div>
    </div>
  )
}

type ComboBoxFieldProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  TTransformedValues = TFieldValues,
> = {
  control: Control<TFieldValues, TName, TTransformedValues>
  options: Array<Option>
  name: TName
  className?: string
  onChange?: (value: number) => void
}
export function ComboBoxField<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  TTransformedValues = TFieldValues,
>({
  control,
  className,
  options,
  name,
  onChange,
}: ComboBoxFieldProps<TFieldValues, TName, TTransformedValues>) {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange: fieldOnChange, ...field } }) => (
        <ComboBox
          className={className}
          options={options}
          value={String(value)}
          onChange={value => {
            const parsed = parseInt(value)
            fieldOnChange(parsed)
            onChange?.(parsed)
          }}
          {...field}
        />
      )}
    />
  )
}
