import { useTranslation } from '@/i18n'
import { type FC } from 'react'
import { useSearchParams } from 'react-router'
import { SelectCore } from './core'

export const LanguageSelect: FC = () => {
  const { supportedLanguages, language, setLanguage } = useTranslation()
  const [, setSearchParams] = useSearchParams()

  return (
    <SelectCore
      options={supportedLanguages.map(language => ({
        value: language,
        label: language,
      }))}
      value={language}
      onChange={(language: 'en' | 'ru') => {
        setLanguage(language)
        setSearchParams(
          currentSearchParams => {
            const nextSearchParams = new URLSearchParams(currentSearchParams)
            nextSearchParams.set('language', language)
            return nextSearchParams
          },
          { replace: true },
        )
      }}
    />
  )
}
