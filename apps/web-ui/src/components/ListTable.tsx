import { useTranslation } from '@/i18n'
import { type ReactNode } from 'react'
import { BodyCell, HeadCell, Table } from './core'

type Entity = { id: number }

export type Column<T extends Entity> = {
  title: string
  cell: (row: T) => ReactNode
  textAlign?: 'start' | 'center' | 'end'
}

type ListTableProps<T extends Entity> = {
  columns: Array<Column<T>>
  rows: Array<T> | undefined
  onRowSelect?: (row: T) => void
}
export function ListTable<T extends Entity>({
  columns,
  rows = [],
  onRowSelect,
}: ListTableProps<T>): ReactNode {
  const { t } = useTranslation()
  return (
    <Table>
      <thead>
        <tr>
          {columns.map((column, columnIndex) => (
            <HeadCell key={columnIndex}>{column.title}</HeadCell>
          ))}
        </tr>
      </thead>
      <tbody>
        {rows.length === 0 ? (
          <tr>
            <BodyCell colSpan={columns.length}>{t('common.no_data')}</BodyCell>
          </tr>
        ) : (
          rows.map(row => (
            <tr
              key={row.id}
              onClick={() => {
                onRowSelect?.(row)
              }}
            >
              {columns.map((column, columnIndex) => (
                <BodyCell key={columnIndex}>{column.cell(row)}</BodyCell>
              ))}
            </tr>
          ))
        )}
      </tbody>
    </Table>
  )
}
