import { DATETIME_FORMAT } from '@/constants'
import { useTranslation } from '@/i18n'
import { formatInTimeZone } from 'date-fns-tz'
import { type ComponentType } from 'react'
import { List, ListItem } from './core'

export const BuildInfo: ComponentType = () => {
  const { t } = useTranslation()
  return (
    <List>
      <ListItem>
        <span>{t('common.commit')}:</span>
        <b>{import.meta.env.VITE_APP_BUILD_HASH}</b>
      </ListItem>
      <ListItem>
        <span>{t('common.date')}:</span>
        <b>
          {formatInTimeZone(
            import.meta.env.VITE_APP_BUILD_TIMESTAMP,
            'UTC',
            DATETIME_FORMAT,
          )}
        </b>
      </ListItem>
    </List>
  )
}
