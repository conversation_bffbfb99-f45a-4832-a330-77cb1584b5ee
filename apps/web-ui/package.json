{"name": "@cloudretail/web-ui", "version": "1.0.0", "private": true, "scripts": {"build": "vite build", "lint": "DEBUG=eslint:cli-engine eslint --fix .", "start": "npm-run-all -p typecheck:w vite", "test": "vitest", "typecheck": "tsc --noEmit", "typecheck:w": "tsc --noEmit --preserveWatchOutput --watch", "vite": "vite"}, "devDependencies": {"@cloudretail/api": "*", "@eslint/js": "~9.36.0", "@ravshansbox/react-i18n": "~0.6.2", "@reduxjs/toolkit": "~2.9.0", "@tailwindcss/vite": "~4.1.13", "@tanstack/react-query": "~5.89.0", "@trpc/client": "~11.5.1", "@trpc/react-query": "~11.5.1", "@tw-classed/react": "~1.8.1", "@types/react": "~19.1.13", "@types/react-dom": "~19.1.9", "@vitejs/plugin-react": "~5.0.3", "date-fns": "~4.1.0", "date-fns-tz": "~3.2.0", "eslint": "~9.36.0", "npm-run-all": "~4.1.5", "postcss": "~8.5.6", "react": "~19.1.1", "react-dom": "~19.1.1", "react-hook-form": "~7.62.0", "react-number-format": "~5.4.4", "react-redux": "~9.2.0", "react-router": "~7.9.1", "tailwind-merge": "~3.3.1", "tailwindcss": "~4.1.13", "typescript": "~5.9.2", "typescript-eslint": "~8.44.0", "vite": "~7.1.6", "vitest": "~3.2.4"}}