import tailwindcss from '@tailwindcss/vite'
import viteReact from '@vitejs/plugin-react'
import { execSync } from 'node:child_process'
import process from 'node:process'
import url from 'node:url'

/** @type {import('vite').UserConfig} */
export default {
  build: {
    rollupOptions: {
      output: {
        manualChunks: id => {
          if (/node_modules/.test(id)) {
            return 'vendor'
          }
        },
      },
    },
  },
  clearScreen: false,
  define: {
    'import.meta.env.VITE_APP_BUILD_HASH': JSON.stringify(
      process.env.VITE_APP_BUILD_HASH ||
        (() => {
          try {
            return execSync('git rev-parse --short HEAD', {
              encoding: 'utf8',
            }).trim()
          } catch {
            return 'unknown'
          }
        })(),
    ),
    'import.meta.env.VITE_APP_BUILD_TIMESTAMP': JSON.stringify(
      process.env.VITE_APP_BUILD_TIMESTAMP || new Date().toISOString(),
    ),
  },
  plugins: [viteReact(), tailwindcss()],
  resolve: {
    alias: [
      {
        find: '@',
        replacement: url.fileURLToPath(new url.URL('./src', import.meta.url)),
      },
    ],
  },
  server: {
    proxy: {
      '^/api/.*': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, ''),
      },
    },
  },
}
